@echo off
chcp 65001 > nul
echo ========================================
echo Recompile with Better Button Layout
echo ========================================
echo.

echo Step 1: Clean previous build
if exist "ChatLauncher\bin" rmdir /s /q "ChatLauncher\bin"
if exist "ChatLauncher\obj" rmdir /s /q "ChatLauncher\obj"
echo Build folders cleaned.

echo.
echo Step 2: Button layout improvements made:
echo ✓ Increased button padding: 15,8 → 20,12
echo ✓ Added minimum button size: MinWidth=100, MinHeight=36
echo ✓ Improved button margins: 5 → 8-15px spacing
echo ✓ Enhanced launch button: MinWidth=150, MinHeight=45
echo ✓ Increased card padding: 15 → 20px
echo ✓ Better visual spacing throughout interface

echo.
echo Step 3: Compile ChatLauncher with improved button layout
echo Command: msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal
echo.

msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ ChatLauncher recompiled successfully with better button layout!
    
    if exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
        echo ✓ Output file created: ChatLauncher\bin\Debug\ChatLauncher.exe
        
        echo.
        echo ========================================
        echo SUCCESS: Improved Button Layout!
        echo ========================================
        echo.
        echo Button improvements:
        echo ✓ Larger button sizes for better visibility
        echo ✓ Improved spacing between buttons
        echo ✓ Enhanced padding for better touch targets
        echo ✓ Consistent button styling throughout
        echo ✓ Better visual hierarchy
        echo.
        echo Interface improvements:
        echo ✓ More spacious card layouts
        echo ✓ Better button grouping
        echo ✓ Improved readability
        echo ✓ Enhanced user experience
        echo.
        
        set /p choice=Do you want to start the improved ChatLauncher? (y/n): 
        if /i "%choice%"=="y" (
            echo.
            echo Starting ChatLauncher with improved button layout...
            echo.
            echo You should now see:
            echo • Larger, more visible buttons
            echo • Better spacing between interface elements
            echo • Improved button text readability
            echo • More professional appearance
            echo • Better touch/click targets
            echo.
            start ChatLauncher\bin\Debug\ChatLauncher.exe
            echo.
            echo ✓ ChatLauncher started with improved layout!
            echo.
            echo The buttons should now be:
            echo • More visible and easier to read
            echo • Better spaced for comfortable clicking
            echo • Consistently sized throughout the interface
            echo • More professional in appearance
        ) else (
            echo.
            echo ChatLauncher with improved button layout is ready!
            echo Execute: ChatLauncher\bin\Debug\ChatLauncher.exe
        )
    ) else (
        echo ✗ Output file not found
        echo This indicates a compilation issue.
    )
) else (
    echo.
    echo ✗ Compilation failed!
    echo Please check the error messages above for specific issues.
)

echo.
echo ========================================
echo Button Layout Improvement Complete
echo ========================================
echo.
echo Changes made:
echo.
echo 1. Button Style (LauncherButtonStyle):
echo    • Padding: 15,8 → 20,12 (more comfortable)
echo    • Margin: 5 → 8 (better spacing)
echo    • MinWidth: 100px (consistent sizing)
echo    • MinHeight: 36px (better touch targets)
echo.
echo 2. Specific Button Improvements:
echo    • Header buttons: 10px spacing between
echo    • Config buttons: 10px spacing between
echo    • Launch button: 150px width, 45px height
echo    • Status buttons: 15px spacing between
echo.
echo 3. Card Layout:
echo    • Margin: 5 → 8px (better separation)
echo    • Padding: 15 → 20px (more spacious)
echo.
echo 4. Visual Improvements:
echo    • Better button hierarchy
echo    • Improved readability
echo    • More professional appearance
echo    • Enhanced user experience
echo.
echo The interface should now look more polished and be easier to use!
pause
