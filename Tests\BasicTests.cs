using System;
using System.Threading.Tasks;
using Chat.Models;
using Chat.Services;
using Chat.ViewModels;

namespace Chat.Tests
{
    /// <summary>
    /// 基本功能测试类
    /// </summary>
    public class BasicTests
    {
        public static async Task RunAllTests()
        {
            Console.WriteLine("开始运行基本功能测试...");
            
            try
            {
                TestModels();
                await TestServices();
                await TestViewModel();
                
                Console.WriteLine("所有测试通过！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
                throw;
            }
        }

        private static void TestModels()
        {
            Console.WriteLine("测试Models...");
            
            // 测试User模型
            var user = new User("testuser", "Test User");
            if (string.IsNullOrEmpty(user.Username) || string.IsNullOrEmpty(user.DisplayName))
                throw new Exception("User模型测试失败");
            
            // 测试ChatMessage模型
            var message = new ChatMessage("Hello World", user.DisplayName);
            if (string.IsNullOrEmpty(message.Content) || string.IsNullOrEmpty(message.Sender))
                throw new Exception("ChatMessage模型测试失败");
            
            Console.WriteLine("Models测试通过");
        }

        private static async Task TestServices()
        {
            Console.WriteLine("测试Services...");
            
            var chatService = new ChatService();
            
            // 测试获取消息
            var messages = await chatService.GetMessagesAsync();
            if (messages == null || messages.Count == 0)
                throw new Exception("获取消息失败");
            
            // 测试获取用户
            var users = await chatService.GetUsersAsync();
            if (users == null || users.Count == 0)
                throw new Exception("获取用户失败");
            
            // 测试发送消息
            var newMessage = new ChatMessage("Test message", "Test User");
            var result = await chatService.SendMessageAsync(newMessage);
            if (!result)
                throw new Exception("发送消息失败");
            
            Console.WriteLine("Services测试通过");
        }

        private static async Task TestViewModel()
        {
            Console.WriteLine("测试ViewModel...");
            
            var chatService = new ChatService();
            var viewModel = new MainWindowViewModel(chatService);
            
            // 等待数据加载
            await Task.Delay(200);
            
            if (string.IsNullOrEmpty(viewModel.Title))
                throw new Exception("ViewModel Title为空");
            
            if (string.IsNullOrEmpty(viewModel.BrowserUrl))
                throw new Exception("ViewModel BrowserUrl为空");
            
            if (viewModel.LoadUrlCommand == null)
                throw new Exception("LoadUrlCommand为空");
            
            Console.WriteLine("ViewModel测试通过");
        }
    }
}
