using System;
using Chat.Models;
using Chat.Services;

namespace Chat
{
    // 这是一个简单的测试类，用于验证我们的模型和服务是否正常工作
    public class TestConsole
    {
        public static void TestModelsAndServices()
        {
            try
            {
                // 测试模型
                var user = new User("testuser", "Test User");
                var message = new ChatMessage("Hello World", user.DisplayName);
                
                Console.WriteLine($"User: {user.DisplayName}, Status: {user.Status}");
                Console.WriteLine($"Message: {message.Content}, Sender: {message.Sender}, Time: {message.Timestamp}");
                
                // 测试服务
                var chatService = new ChatService();
                var messages = chatService.GetMessagesAsync().Result;
                var users = chatService.GetUsersAsync().Result;
                
                Console.WriteLine($"Loaded {messages.Count} messages and {users.Count} users");
                
                Console.WriteLine("Models and Services test passed!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Test failed: {ex.Message}");
            }
        }
    }
}
