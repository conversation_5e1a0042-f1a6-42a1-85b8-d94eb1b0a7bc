using CefSharp;
using CefSharp.Wpf;
using System;
using System.Reflection;
using System.Threading.Tasks;

namespace Chat.Services
{
    /// <summary>
    /// CefSharp API兼容性助手
    /// 处理不同版本CefSharp之间的API差异
    /// </summary>
    public static class CefSharpCompatibilityHelper
    {
        /// <summary>
        /// 安全地执行JavaScript脚本
        /// </summary>
        public static async Task<JavascriptResponse> SafeEvaluateScriptAsync(ChromiumWebBrowser browser, string script)
        {
            try
            {
                // 检查浏览器是否已初始化
                if (!browser.IsBrowserInitialized)
                {
                    return new JavascriptResponse
                    {
                        Success = false,
                        Message = "浏览器尚未初始化"
                    };
                }

                // 尝试使用不同的API方法
                return await TryEvaluateScript(browser, script);
            }
            catch (Exception ex)
            {
                return new JavascriptResponse
                {
                    Success = false,
                    Message = $"脚本执行失败: {ex.Message}"
                };
            }
        }

        private static async Task<JavascriptResponse> TryEvaluateScript(ChromiumWebBrowser browser, string script)
        {
            // 方法1: 尝试直接调用EvaluateScriptAsync
            try
            {
                var directMethod = browser.GetType().GetMethod("EvaluateScriptAsync", new[] { typeof(string) });
                if (directMethod != null)
                {
                    var task = (Task<JavascriptResponse>)directMethod.Invoke(browser, new object[] { script });
                    return await task;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"直接调用EvaluateScriptAsync失败: {ex.Message}");
            }

            // 方法2: 尝试通过GetMainFrame调用
            try
            {
                var getMainFrameMethod = browser.GetType().GetMethod("GetMainFrame");
                if (getMainFrameMethod != null)
                {
                    var mainFrame = getMainFrameMethod.Invoke(browser, null);
                    if (mainFrame != null)
                    {
                        var evaluateMethod = mainFrame.GetType().GetMethod("EvaluateScriptAsync", new[] { typeof(string) });
                        if (evaluateMethod != null)
                        {
                            var task = (Task<JavascriptResponse>)evaluateMethod.Invoke(mainFrame, new object[] { script });
                            return await task;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"通过MainFrame调用EvaluateScriptAsync失败: {ex.Message}");
            }

            // 方法3: 尝试使用ExecuteScriptAsync（如果存在）
            try
            {
                var executeMethod = browser.GetType().GetMethod("ExecuteScriptAsync", new[] { typeof(string) });
                if (executeMethod != null)
                {
                    var task = (Task<JavascriptResponse>)executeMethod.Invoke(browser, new object[] { script });
                    return await task;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ExecuteScriptAsync调用失败: {ex.Message}");
            }

            // 如果所有方法都失败，返回错误
            return new JavascriptResponse
            {
                Success = false,
                Message = "无法找到兼容的JavaScript执行方法"
            };
        }

        /// <summary>
        /// 安全地加载URL
        /// </summary>
        public static void SafeLoadUrl(ChromiumWebBrowser browser, string url)
        {
            try
            {
                // 尝试使用Load方法
                var loadMethod = browser.GetType().GetMethod("Load", new[] { typeof(string) });
                if (loadMethod != null)
                {
                    loadMethod.Invoke(browser, new object[] { url });
                    return;
                }

                // 尝试使用LoadUrl方法
                var loadUrlMethod = browser.GetType().GetMethod("LoadUrl", new[] { typeof(string) });
                if (loadUrlMethod != null)
                {
                    loadUrlMethod.Invoke(browser, new object[] { url });
                    return;
                }

                // 尝试设置Address属性
                var addressProperty = browser.GetType().GetProperty("Address");
                if (addressProperty != null && addressProperty.CanWrite)
                {
                    addressProperty.SetValue(browser, url);
                    return;
                }

                throw new NotSupportedException("无法找到兼容的URL加载方法");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载URL失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取CefSharp版本信息
        /// </summary>
        public static string GetCefSharpVersion()
        {
            try
            {
                var assembly = Assembly.GetAssembly(typeof(ChromiumWebBrowser));
                var version = assembly?.GetName()?.Version?.ToString() ?? "Unknown";
                return $"CefSharp Version: {version}";
            }
            catch (Exception ex)
            {
                return $"无法获取版本信息: {ex.Message}";
            }
        }

        /// <summary>
        /// 检查API兼容性
        /// </summary>
        public static string CheckApiCompatibility(ChromiumWebBrowser browser)
        {
            var report = "CefSharp API兼容性检查:\n";
            
            try
            {
                // 检查EvaluateScriptAsync方法
                var evaluateMethod = browser.GetType().GetMethod("EvaluateScriptAsync", new[] { typeof(string) });
                report += $"- EvaluateScriptAsync: {(evaluateMethod != null ? "✓ 可用" : "✗ 不可用")}\n";

                // 检查GetMainFrame方法
                var getMainFrameMethod = browser.GetType().GetMethod("GetMainFrame");
                report += $"- GetMainFrame: {(getMainFrameMethod != null ? "✓ 可用" : "✗ 不可用")}\n";

                // 检查Load方法
                var loadMethod = browser.GetType().GetMethod("Load", new[] { typeof(string) });
                report += $"- Load: {(loadMethod != null ? "✓ 可用" : "✗ 不可用")}\n";

                // 检查LoadUrl方法
                var loadUrlMethod = browser.GetType().GetMethod("LoadUrl", new[] { typeof(string) });
                report += $"- LoadUrl: {(loadUrlMethod != null ? "✓ 可用" : "✗ 不可用")}\n";

                // 检查IsBrowserInitialized属性
                var isBrowserInitializedProperty = browser.GetType().GetProperty("IsBrowserInitialized");
                report += $"- IsBrowserInitialized: {(isBrowserInitializedProperty != null ? "✓ 可用" : "✗ 不可用")}\n";

                report += GetCefSharpVersion();
            }
            catch (Exception ex)
            {
                report += $"检查过程中出错: {ex.Message}";
            }

            return report;
        }
    }
}
