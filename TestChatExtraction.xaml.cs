using Chat.Models;
using Chat.Services;
using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace Chat
{
    public partial class TestChatExtraction : Window
    {
        private WebChatExtractor _extractor;
        private ObservableCollection<ChatMessage> _extractedMessages;
        private bool _isExtracting = false;

        public TestChatExtraction()
        {
            InitializeComponent();
            InitializeData();
        }

        private void InitializeData()
        {
            _extractedMessages = new ObservableCollection<ChatMessage>();
            ExtractedMessagesList.ItemsSource = _extractedMessages;
            
            // 设置默认选择
            PlatformComboBox.SelectedIndex = 0;
        }

        private void LoadButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedItem = PlatformComboBox.SelectedItem as ComboBoxItem;
            if (selectedItem != null)
            {
                var url = selectedItem.Tag.ToString();
                var platformName = selectedItem.Content.ToString();
                
                StatusText.Text = $"正在加载 {platformName}...";
                Browser.LoadUrl(url);
                
                // 创建提取器
                _extractor = new WebChatExtractor(Browser, platformName);
                _extractor.MessageExtracted += OnMessageExtracted;
                _extractor.StatusChanged += OnStatusChanged;
            }
        }

        private async void StartExtractionButton_Click(object sender, RoutedEventArgs e)
        {
            if (_extractor == null)
            {
                StatusText.Text = "请先加载平台";
                return;
            }

            if (!_isExtracting)
            {
                StatusText.Text = "正在启动消息提取...";
                var success = await _extractor.InjectExtractionScriptAsync();
                
                if (success)
                {
                    _isExtracting = true;
                    StatusText.Text = "消息提取已启动";
                    StartExtractionButton.IsEnabled = false;
                    StopExtractionButton.IsEnabled = true;
                }
                else
                {
                    StatusText.Text = "启动失败";
                }
            }
        }

        private void StopExtractionButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isExtracting)
            {
                // 停止提取
                _extractor?.StopExtraction();
                _isExtracting = false;
                StatusText.Text = "消息提取已停止";
                StartExtractionButton.IsEnabled = true;
                StopExtractionButton.IsEnabled = false;
            }
        }

        private void OnMessageExtracted(object sender, ChatMessage message)
        {
            Dispatcher.Invoke(() =>
            {
                _extractedMessages.Add(message);
                UpdateStatistics();
                
                // 自动滚动到最新消息
                if (ExtractedMessagesList.Items.Count > 0)
                {
                    ExtractedMessagesList.ScrollIntoView(ExtractedMessagesList.Items[ExtractedMessagesList.Items.Count - 1]);
                }
            });
        }

        private void OnStatusChanged(object sender, string status)
        {
            Dispatcher.Invoke(() =>
            {
                StatusText.Text = status;
            });
        }

        private void UpdateStatistics()
        {
            MessageCountText.Text = $"消息数: {_extractedMessages.Count}";
            LastUpdateText.Text = $"最后更新: {DateTime.Now:HH:mm:ss}";
        }
    }
}
