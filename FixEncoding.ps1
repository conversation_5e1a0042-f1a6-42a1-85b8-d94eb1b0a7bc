# PowerShell script to fix file encoding issues
# Run this script to convert all source files to UTF-8 without BOM

Write-Host "========================================" -ForegroundColor Green
Write-Host "File Encoding Fix Tool (PowerShell)" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "Note: Running without administrator privileges" -ForegroundColor Yellow
}

# Function to fix file encoding
function Fix-FileEncoding {
    param(
        [string]$FilePath
    )
    
    try {
        # Read file content
        $content = Get-Content -Path $FilePath -Raw -Encoding UTF8
        
        # Write back with UTF-8 without BOM
        $utf8NoBom = New-Object System.Text.UTF8Encoding $false
        [System.IO.File]::WriteAllText($FilePath, $content, $utf8NoBom)
        
        Write-Host "Fixed: $FilePath" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Error fixing $FilePath : $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Get all C# source files
$sourceFiles = @()
$sourceFiles += Get-ChildItem -Path "ChatLauncher" -Filter "*.cs" -Recurse -ErrorAction SilentlyContinue
$sourceFiles += Get-ChildItem -Path "." -Filter "*.cs" -Recurse -ErrorAction SilentlyContinue | Where-Object { $_.FullName -notlike "*\ChatLauncher\*" }

Write-Host "Found $($sourceFiles.Count) C# source files" -ForegroundColor Cyan
Write-Host ""

if ($sourceFiles.Count -eq 0) {
    Write-Host "No C# files found to process" -ForegroundColor Yellow
    exit
}

# Ask for confirmation
$response = Read-Host "Do you want to fix encoding for all C# files? (y/n)"
if ($response -ne 'y' -and $response -ne 'Y') {
    Write-Host "Operation cancelled" -ForegroundColor Yellow
    exit
}

Write-Host ""
Write-Host "Processing files..." -ForegroundColor Cyan

$successCount = 0
$errorCount = 0

foreach ($file in $sourceFiles) {
    if (Fix-FileEncoding -FilePath $file.FullName) {
        $successCount++
    } else {
        $errorCount++
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Encoding Fix Results" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "Successfully fixed: $successCount files" -ForegroundColor Green
Write-Host "Errors: $errorCount files" -ForegroundColor Red
Write-Host ""

# Fix project files
$projectFiles = @()
$projectFiles += Get-ChildItem -Path "." -Filter "*.csproj" -Recurse -ErrorAction SilentlyContinue
$projectFiles += Get-ChildItem -Path "." -Filter "*.sln" -ErrorAction SilentlyContinue

if ($projectFiles.Count -gt 0) {
    Write-Host "Found $($projectFiles.Count) project files" -ForegroundColor Cyan
    
    $response = Read-Host "Do you want to fix encoding for project files too? (y/n)"
    if ($response -eq 'y' -or $response -eq 'Y') {
        foreach ($file in $projectFiles) {
            if (Fix-FileEncoding -FilePath $file.FullName) {
                Write-Host "Fixed project file: $($file.Name)" -ForegroundColor Green
            }
        }
    }
}

Write-Host ""
Write-Host "Recommendations:" -ForegroundColor Yellow
Write-Host "1. Close Visual Studio if it's open" -ForegroundColor White
Write-Host "2. Delete bin and obj folders" -ForegroundColor White
Write-Host "3. Reopen Visual Studio" -ForegroundColor White
Write-Host "4. Rebuild the solution" -ForegroundColor White
Write-Host ""

# Offer to clean build folders
$response = Read-Host "Do you want to clean bin/obj folders now? (y/n)"
if ($response -eq 'y' -or $response -eq 'Y') {
    Write-Host ""
    Write-Host "Cleaning build folders..." -ForegroundColor Cyan
    
    $foldersToClean = @("bin", "obj", "ChatLauncher\bin", "ChatLauncher\obj")
    
    foreach ($folder in $foldersToClean) {
        if (Test-Path $folder) {
            try {
                Remove-Item -Path $folder -Recurse -Force
                Write-Host "Deleted: $folder" -ForegroundColor Green
            }
            catch {
                Write-Host "Could not delete $folder : $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
    
    Write-Host "Build folders cleaned!" -ForegroundColor Green
}

Write-Host ""
Write-Host "Encoding fix completed!" -ForegroundColor Green
Write-Host "You can now open Visual Studio and rebuild the solution." -ForegroundColor White

# Keep window open
Read-Host "Press Enter to exit"
