@echo off
chcp 65001 > nul
echo ========================================
echo Compile with Fixed Default Values
echo ========================================
echo.

echo Step 1: Clean previous build
if exist "ChatLauncher\bin" rmdir /s /q "ChatLauncher\bin"
if exist "ChatLauncher\obj" rmdir /s /q "ChatLauncher\obj"
echo Build folders cleaned.

echo.
echo Step 2: Default configuration values fixed:
echo ✓ Discord Web: URL + Debug=false
echo ✓ Facebook Messenger: URL + Debug=false
echo ✓ Telegram Web: URL + Debug=false
echo ✓ WhatsApp Web: URL + Debug=false
echo ✓ HTTP Proxy Test: URL + Proxy=http://127.0.0.1:8080 + Debug=true
echo ✓ SOCKS5 Proxy Test: URL + Proxy=socks5://127.0.0.1:1080 + Debug=true
echo ✓ Browser Fingerprint Test: URL + Debug=true + Window Size=1280x720
echo ✓ Default Launch: Debug=false

echo.
echo Step 3: Display logic improvements:
echo ✓ Changed "None" to "无" for better Chinese localization
echo ✓ Changed "Yes/No" to "是/否" for Chinese interface
echo ✓ Force regenerate default configs on every startup
echo ✓ Better error handling for XML serialization
echo ✓ Improved null value handling

echo.
echo Step 4: Compile ChatLauncher with fixed default values
echo Command: msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal
echo.

msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ ChatLauncher compiled successfully with fixed default values!
    
    if exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
        echo ✓ Output file created: ChatLauncher\bin\Debug\ChatLauncher.exe
        
        echo.
        echo ========================================
        echo SUCCESS: Default Values Fixed!
        echo ========================================
        echo.
        echo 🎯 Fixed Default Configuration Values:
        echo.
        echo 🎮 Discord Web:
        echo    • URL: https://discord.com/app
        echo    • 代理: 无
        echo    • 调试: 否
        echo    • 使用次数: 0
        echo.
        echo 📘 Facebook Messenger:
        echo    • URL: https://www.messenger.com
        echo    • 代理: 无
        echo    • 调试: 否
        echo    • 使用次数: 0
        echo.
        echo ✈️ Telegram Web:
        echo    • URL: https://web.telegram.org
        echo    • 代理: 无
        echo    • 调试: 否
        echo    • 使用次数: 0
        echo.
        echo 💬 WhatsApp Web:
        echo    • URL: https://web.whatsapp.com
        echo    • 代理: 无
        echo    • 调试: 否
        echo    • 使用次数: 0
        echo.
        echo 🌐 代理测试 (HTTP):
        echo    • URL: https://www.browserscan.net/zh
        echo    • 代理: http://127.0.0.1:8080
        echo    • 调试: 是
        echo    • 使用次数: 0
        echo.
        echo 🔒 代理测试 (SOCKS5):
        echo    • URL: https://www.browserscan.net/zh
        echo    • 代理: socks5://127.0.0.1:1080
        echo    • 调试: 是
        echo    • 使用次数: 0
        echo.
        echo 🔍 浏览器指纹测试:
        echo    • URL: https://www.browserscan.net/zh
        echo    • 代理: 无
        echo    • 调试: 是
        echo    • 窗口大小: 1280x720
        echo    • 使用次数: 0
        echo.
        echo 🚀 默认启动:
        echo    • URL: 无
        echo    • 代理: 无
        echo    • 调试: 否
        echo    • 使用次数: 0
        echo.
        
        set /p choice=Launch ChatLauncher to test fixed default values? (y/n): 
        if /i "%choice%"=="y" (
            echo.
            echo Starting ChatLauncher with fixed default values...
            echo.
            echo 🎉 Test the following improvements:
            echo.
            echo 1. 📋 Configuration List:
            echo    • Should see 8 default configurations
            echo    • Each with proper icons and names
            echo    • All configurations should be selectable
            echo.
            echo 2. 📝 Configuration Details:
            echo    • Select any configuration from the list
            echo    • Check the "配置详情" panel on the right
            echo    • Should see proper values instead of "None"
            echo    • Chinese localization: "无" instead of "None"
            echo    • Chinese localization: "是/否" instead of "Yes/No"
            echo.
            echo 3. 🌐 Proxy Test Configurations:
            echo    • Select "代理测试 (HTTP)" - should show proxy settings
            echo    • Select "代理测试 (SOCKS5)" - should show proxy settings
            echo    • Both should have browserscan.net URL
            echo.
            echo 4. 🔍 Browser Fingerprint Test:
            echo    • Should show browserscan.net URL
            echo    • Should show 1280x720 window size
            echo    • Debug mode should be enabled
            echo.
            echo 5. 💾 Configuration Persistence:
            echo    • Default configs regenerated on startup
            echo    • Should always show correct values
            echo    • No more missing parameters
            echo.
            start ChatLauncher\bin\Debug\ChatLauncher.exe
            echo.
            echo ✅ ChatLauncher launched with fixed default values!
            echo.
            echo 📂 Configuration files location:
            echo    ChatLauncher\bin\Debug\Configs\
            echo    ├── default_configs.xml (regenerated with correct values)
            echo    └── configs.xml (user configurations)
            echo.
            echo 🎯 Expected Results:
            echo • All default configurations show proper values
            echo • No more "None", "No", "0" placeholders
            echo • Chinese localized display text
            echo • Complete configuration parameters
            echo • Professional appearance
        ) else (
            echo.
            echo ChatLauncher with fixed default values is ready!
            echo Execute: ChatLauncher\bin\Debug\ChatLauncher.exe
        )
    ) else (
        echo ✗ Output file not found
        echo This indicates a compilation issue.
    )
) else (
    echo.
    echo ✗ Compilation failed!
    echo Please check the error messages above for specific issues.
)

echo.
echo ========================================
echo Default Values Fix Summary
echo ========================================
echo.
echo 🔧 Issues Fixed:
echo    ✅ Missing URL parameters in default configs
echo    ✅ Missing proxy settings for test configs
echo    ✅ Missing debug mode settings
echo    ✅ Missing window size for fingerprint test
echo    ✅ Poor localization (English → Chinese)
echo.
echo 📊 Configuration Improvements:
echo.
echo 1. 🌐 Complete URL Settings:
echo    • All web app configs have proper URLs
echo    • Proxy test configs use browserscan.net
echo    • Fingerprint test has testing URL
echo.
echo 2. 🔧 Proxy Configuration:
echo    • HTTP proxy: http://127.0.0.1:8080
echo    • SOCKS5 proxy: socks5://127.0.0.1:1080
echo    • Ready for local proxy testing
echo.
echo 3. 🎯 Debug Settings:
echo    • Web apps: Debug disabled (production-like)
echo    • Test configs: Debug enabled (development)
echo    • Appropriate for each use case
echo.
echo 4. 📐 Window Settings:
echo    • Fingerprint test: 1280x720 (standard size)
echo    • Optimal for testing and compatibility
echo.
echo 5. 🌏 Localization:
echo    • "无" instead of "None"
echo    • "是/否" instead of "Yes/No"
echo    • Better Chinese user experience
echo.
echo 🏆 Quality Improvements:
echo    • Force regenerate defaults (always current)
echo    • Better error handling
echo    • Improved null value handling
echo    • Professional configuration display
echo.
echo All default configuration values are now properly set! 🎉
pause
