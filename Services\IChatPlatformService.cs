using Chat.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Chat.Services
{
    public interface IChatPlatformService
    {
        string PlatformName { get; }
        string WebUrl { get; }
        bool IsLoggedIn { get; }
        
        Task<bool> InitializeAsync();
        Task<List<ChatMessage>> GetRecentMessagesAsync();
        Task<bool> SendMessageAsync(string content, string chatId = null);
        Task<List<ChatContact>> GetContactsAsync();
        
        event EventHandler<ChatMessage> NewMessageReceived;
        event EventHandler<bool> LoginStatusChanged;
    }
    
    public class ChatContact
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Avatar { get; set; }
        public DateTime LastSeen { get; set; }
        public bool IsOnline { get; set; }
        public bool IsGroup { get; set; }
    }
}
