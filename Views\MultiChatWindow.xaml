<Window x:Class="Chat.Views.MultiChatWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:Chat.Controls"
        Title="多平台聊天集成" Height="800" Width="1200">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="200"/>
        </Grid.RowDefinitions>
        
        <!-- 工具栏 -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="10">
            <StackPanel Orientation="Horizontal">
                <Button x:Name="AddPlatformButton" Content="添加平台" Margin="0,0,10,0" 
                       Click="AddPlatformButton_Click"/>
                <Button x:Name="StartAllButton" Content="全部开始监听" Margin="0,0,10,0" 
                       Click="StartAllButton_Click"/>
                <Button x:Name="StopAllButton" Content="全部停止监听" Margin="0,0,10,0" 
                       Click="StopAllButton_Click"/>
                <TextBlock x:Name="TotalMessagesText" Text="总消息: 0" 
                          Foreground="White" VerticalAlignment="Center" Margin="20,0,0,0"/>
            </StackPanel>
        </Border>
        
        <!-- 平台标签页 -->
        <TabControl x:Name="PlatformTabs" Grid.Row="1" Background="#ECF0F1">
            <!-- 动态添加平台标签页 -->
        </TabControl>
        
        <!-- 统一消息面板 -->
        <Border Grid.Row="2" Background="#34495E">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="200"/>
                </Grid.ColumnDefinitions>
                
                <!-- 消息列表 -->
                <ScrollViewer Grid.Column="0" VerticalScrollBarVisibility="Auto">
                    <ListBox x:Name="UnifiedMessagesList" Background="Transparent" 
                            BorderThickness="0" Margin="10">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Background="#3498DB" CornerRadius="5" Padding="10" Margin="0,2">
                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="{Binding Sender}" FontWeight="Bold" 
                                                      Foreground="White" Margin="0,0,10,0"/>
                                            <TextBlock Text="{Binding Platform}" FontSize="10" 
                                                      Foreground="#BDC3C7" Margin="0,0,10,0"/>
                                            <TextBlock Text="{Binding Timestamp, StringFormat=HH:mm:ss}" 
                                                      FontSize="10" Foreground="#BDC3C7"/>
                                        </StackPanel>
                                        <TextBlock Text="{Binding Content}" Foreground="White" 
                                                  TextWrapping="Wrap" Margin="0,5,0,0"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </ScrollViewer>
                
                <!-- 统计面板 -->
                <Border Grid.Column="1" Background="#2C3E50" Padding="10">
                    <StackPanel>
                        <TextBlock Text="消息统计" Foreground="White" FontWeight="Bold" 
                                  FontSize="14" Margin="0,0,0,10"/>
                        <StackPanel x:Name="PlatformStats">
                            <!-- 动态添加平台统计 -->
                        </StackPanel>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
    </Grid>
</Window>
