using System;

namespace Chat.Models
{
    public class User
    {
        public int Id { get; set; }
        public string Username { get; set; }
        public string DisplayName { get; set; }
        public string Email { get; set; }
        public DateTime LastSeen { get; set; }
        public UserStatus Status { get; set; }

        public User()
        {
            LastSeen = DateTime.Now;
            Status = UserStatus.Offline;
        }

        public User(string username, string displayName) : this()
        {
            Username = username;
            DisplayName = displayName;
        }
    }

    public enum UserStatus
    {
        Online,
        Away,
        Busy,
        Offline
    }
}
