@echo off
chcp 65001 > nul
echo ========================================
echo Compilation Test Tool
echo ========================================
echo.

echo Step 1: Clean previous build files...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "ChatLauncher\bin" rmdir /s /q "ChatLauncher\bin"
if exist "ChatLauncher\obj" rmdir /s /q "ChatLauncher\obj"
echo Build folders cleaned.
echo.

echo Step 2: Test Chat project compilation...
msbuild Chat.csproj /p:Configuration=Debug /p:Platform=x86 /v:minimal
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Chat project compilation failed!
    echo Please check the error messages above.
    pause
    exit /b 1
) else (
    echo Chat project compiled successfully!
)
echo.

echo Step 3: Test ChatLauncher project compilation...
msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: ChatLauncher project compilation failed!
    echo Please check the error messages above.
    pause
    exit /b 1
) else (
    echo ChatLauncher project compiled successfully!
)
echo.

echo Step 4: Test solution compilation...
msbuild Chat.sln /p:Configuration=Debug /v:minimal
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Solution compilation failed!
    echo Please check the error messages above.
    pause
    exit /b 1
) else (
    echo Solution compiled successfully!
)
echo.

echo Step 5: Verify output files...
if exist "bin\x86\Debug\Chat.exe" (
    echo ✓ Chat.exe found
) else (
    echo ✗ Chat.exe not found
)

if exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
    echo ✓ ChatLauncher.exe found
) else (
    echo ✗ ChatLauncher.exe not found
)
echo.

echo ========================================
echo Compilation Test Complete
echo ========================================
echo.

if exist "bin\x86\Debug\Chat.exe" if exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
    echo SUCCESS: Both applications compiled successfully!
    echo.
    echo You can now:
    echo 1. Run ChatLauncher: ChatLauncher\bin\Debug\ChatLauncher.exe
    echo 2. Run Chat directly: bin\x86\Debug\Chat.exe
    echo.
    set /p choice=Do you want to start ChatLauncher now? (y/n): 
    if /i "%choice%"=="y" (
        echo Starting ChatLauncher...
        start ChatLauncher\bin\Debug\ChatLauncher.exe
    )
) else (
    echo FAILED: One or more applications failed to compile.
    echo Please check the error messages and fix any issues.
)

echo.
pause
