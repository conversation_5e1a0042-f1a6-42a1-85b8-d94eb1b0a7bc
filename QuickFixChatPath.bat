@echo off
chcp 65001 > nul
echo ========================================
echo Quick Fix Chat Path
echo ========================================
echo.

echo Step 1: Verify Chat.exe exists
if exist "bin\x86\Debug\Chat.exe" (
    echo ✓ Chat.exe found at: bin\x86\Debug\Chat.exe
) else (
    echo ✗ Chat.exe NOT found at: bin\x86\Debug\Chat.exe
    echo Please compile Chat project first!
    pause
    exit /b 1
)

echo.
echo Step 2: Verify ChatLauncher.exe exists
if exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
    echo ✓ ChatLauncher.exe found
) else (
    echo ✗ ChatLauncher.exe NOT found
    echo Compiling ChatLauncher...
    msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal
)

echo.
echo Step 3: Test path from ChatLauncher location
pushd "ChatLauncher\bin\Debug" 2>nul
if exist "..\..\..\bin\x86\Debug\Chat.exe" (
    echo ✓ SUCCESS: Path ..\..\..\bin\x86\Debug\Chat.exe works!
    for %%f in ("..\..\..\bin\x86\Debug\Chat.exe") do echo   Full path: %%~ff
) else (
    echo ✗ FAILED: Path ..\..\..\bin\x86\Debug\Chat.exe does not work
)
popd

echo.
echo Step 4: Recompile ChatLauncher with fixed paths
echo Compiling ChatLauncher...
msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal

if %ERRORLEVEL% EQU 0 (
    echo ✓ ChatLauncher compiled successfully
    echo.
    echo Step 5: Start ChatLauncher to test
    echo Starting ChatLauncher...
    start ChatLauncher\bin\Debug\ChatLauncher.exe
    echo.
    echo ✓ ChatLauncher started!
    echo Please check if it now shows "Chat App: Available" in the status bar.
) else (
    echo ✗ ChatLauncher compilation failed
)

echo.
echo ========================================
echo Quick Fix Complete
echo ========================================
pause
