@echo off
echo 正在尝试编译项目...

REM 方法1: 尝试使用Visual Studio MSBuild
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    echo 使用Visual Studio 2022 MSBuild...
    "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" Chat.sln /p:Configuration=Debug /p:Platform="Any CPU" /v:minimal
    if %ERRORLEVEL% == 0 (
        echo 编译成功！
        goto :success
    )
)

REM 方法2: 尝试使用Visual Studio 2019 MSBuild
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo 使用Visual Studio 2019 MSBuild...
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" Chat.sln /p:Configuration=Debug /p:Platform="Any CPU" /v:minimal
    if %ERRORLEVEL% == 0 (
        echo 编译成功！
        goto :success
    )
)

REM 方法3: 尝试使用.NET Framework MSBuild
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" (
    echo 使用Build Tools MSBuild...
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" Chat.sln /p:Configuration=Debug /p:Platform="Any CPU" /v:minimal
    if %ERRORLEVEL% == 0 (
        echo 编译成功！
        goto :success
    )
)

REM 方法4: 尝试使用系统PATH中的MSBuild
echo 尝试使用系统MSBuild...
msbuild Chat.sln /p:Configuration=Debug /p:Platform="Any CPU" /v:minimal
if %ERRORLEVEL% == 0 (
    echo 编译成功！
    goto :success
)

REM 方法5: 尝试使用dotnet build（可能会有兼容性问题）
echo 尝试使用dotnet build...
dotnet build Chat.sln --configuration Debug --verbosity minimal
if %ERRORLEVEL% == 0 (
    echo 编译成功！
    goto :success
)

echo 所有编译方法都失败了。请检查：
echo 1. 是否安装了Visual Studio或Build Tools
echo 2. 是否安装了.NET Framework 4.8.1
echo 3. 是否有网络连接以下载NuGet包
goto :end

:success
echo.
echo 编译完成！可执行文件位置：
if exist "bin\Debug\Chat.exe" (
    echo bin\Debug\Chat.exe
) else (
    echo 请检查bin目录中的输出文件
)

:end
pause
