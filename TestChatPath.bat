@echo off
chcp 65001 > nul
echo ========================================
echo Chat.exe Path Test
echo ========================================
echo.

echo Current directory: %CD%
echo.

echo 1. Checking if Chat.exe exists in main location:
if exist "bin\x86\Debug\Chat.exe" (
    echo ✓ Found: bin\x86\Debug\Chat.exe
    for %%f in ("bin\x86\Debug\Chat.exe") do echo   Full path: %%~ff
) else (
    echo ✗ Not found: bin\x86\Debug\Chat.exe
)

echo.
echo 2. Checking if ChatLauncher.exe exists:
if exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
    echo ✓ Found: ChatLauncher\bin\Debug\ChatLauncher.exe
    for %%f in ("ChatLauncher\bin\Debug\ChatLauncher.exe") do echo   Full path: %%~ff
) else (
    echo ✗ Not found: ChatLauncher\bin\Debug\ChatLauncher.exe
)

echo.
echo 3. Testing relative paths from ChatLauncher location:
echo Changing to ChatLauncher\bin\Debug directory...
pushd "ChatLauncher\bin\Debug" 2>nul

if %ERRORLEVEL% EQU 0 (
    echo Current directory: %CD%
    echo.
    
    echo Testing path: ..\..\..\bin\x86\Debug\Chat.exe
    if exist "..\..\..\bin\x86\Debug\Chat.exe" (
        echo ✓ SUCCESS: Found Chat.exe via ..\..\..\bin\x86\Debug\Chat.exe
        for %%f in ("..\..\..\bin\x86\Debug\Chat.exe") do echo   Full path: %%~ff
    ) else (
        echo ✗ FAILED: ..\..\..\bin\x86\Debug\Chat.exe not found
    )
    
    echo.
    echo Testing path: ..\..\bin\x86\Debug\Chat.exe
    if exist "..\..\bin\x86\Debug\Chat.exe" (
        echo ✓ SUCCESS: Found Chat.exe via ..\..\bin\x86\Debug\Chat.exe
        for %%f in ("..\..\bin\x86\Debug\Chat.exe") do echo   Full path: %%~ff
    ) else (
        echo ✗ FAILED: ..\..\bin\x86\Debug\Chat.exe not found
    )
    
    echo.
    echo Testing path: ..\bin\x86\Debug\Chat.exe
    if exist "..\bin\x86\Debug\Chat.exe" (
        echo ✓ SUCCESS: Found Chat.exe via ..\bin\x86\Debug\Chat.exe
        for %%f in ("..\bin\x86\Debug\Chat.exe") do echo   Full path: %%~ff
    ) else (
        echo ✗ FAILED: ..\bin\x86\Debug\Chat.exe not found
    )
    
    popd
) else (
    echo ERROR: Could not change to ChatLauncher\bin\Debug directory
)

echo.
echo 4. Directory structure analysis:
echo.
echo From project root:
echo   bin\x86\Debug\Chat.exe          ← Target file
echo   ChatLauncher\bin\Debug\         ← ChatLauncher location
echo.
echo Path calculation:
echo   From: ChatLauncher\bin\Debug\
echo   To:   bin\x86\Debug\Chat.exe
echo.
echo   Step 1: .. (up to ChatLauncher\bin\)
echo   Step 2: .. (up to ChatLauncher\)
echo   Step 3: .. (up to project root)
echo   Step 4: bin\x86\Debug\Chat.exe (down to target)
echo.
echo   Result: ..\..\..\bin\x86\Debug\Chat.exe

echo.
echo ========================================
echo Test Complete
echo ========================================
pause
