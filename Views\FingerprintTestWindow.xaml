<Window x:Class="Chat.Views.FingerprintTestWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:wpf="clr-namespace:CefSharp.Wpf;assembly=CefSharp.Wpf"
        Title="指纹测试" Height="600" Width="1000"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="200"/>
        </Grid.RowDefinitions>
        
        <!-- 工具栏 -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="10">
            <StackPanel Orientation="Horizontal">
                <TextBlock x:Name="FingerprintNameText" Text="指纹测试" Foreground="White" 
                          VerticalAlignment="Center" FontSize="16" FontWeight="Bold" Margin="0,0,20,0"/>
                <Button x:Name="ApplyFingerprintButton" Content="应用指纹" Margin="0,0,10,0"
                        Background="#3498DB" Foreground="White" Padding="10,5" Click="ApplyFingerprintButton_Click"/>
                <Button x:Name="TestFingerprintButton" Content="测试指纹" Margin="0,0,10,0"
                        Background="#E67E22" Foreground="White" Padding="10,5" Click="TestFingerprintButton_Click"/>
                <ComboBox x:Name="TestSiteComboBox" Width="200" Margin="0,0,10,0" SelectedIndex="0">
                    <ComboBoxItem Content="指纹检测网站" Tag="https://browserleaks.com/"/>
                    <ComboBoxItem Content="Canvas指纹测试" Tag="https://browserleaks.com/canvas"/>
                    <ComboBoxItem Content="WebRTC测试" Tag="https://browserleaks.com/webrtc"/>
                    <ComboBoxItem Content="字体检测" Tag="https://browserleaks.com/fonts"/>
                    <ComboBoxItem Content="综合检测" Tag="https://amiunique.org/"/>
                </ComboBox>
                <Button x:Name="LoadTestSiteButton" Content="加载测试网站" Margin="0,0,10,0"
                        Background="#27AE60" Foreground="White" Padding="10,5" Click="LoadTestSiteButton_Click"/>
            </StackPanel>
        </Border>
        
        <!-- 浏览器 -->
        <wpf:ChromiumWebBrowser x:Name="TestBrowser" Grid.Row="1"/>
        
        <!-- 测试结果 -->
        <Border Grid.Row="2" Background="#ECF0F1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="5"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- 指纹信息 -->
                <GroupBox Grid.Column="0" Header="当前指纹信息" Margin="10">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel x:Name="FingerprintInfoPanel" Margin="5">
                            <!-- 指纹信息将在代码中动态添加 -->
                        </StackPanel>
                    </ScrollViewer>
                </GroupBox>
                
                <GridSplitter Grid.Column="1" Background="#BDC3C7" HorizontalAlignment="Stretch"/>
                
                <!-- 测试日志 -->
                <GroupBox Grid.Column="2" Header="测试日志" Margin="10">
                    <ScrollViewer x:Name="LogScrollViewer" VerticalScrollBarVisibility="Auto">
                        <TextBlock x:Name="LogTextBlock" TextWrapping="Wrap" FontFamily="Consolas" 
                                  FontSize="10" Margin="5" Background="Black" Foreground="Lime" Padding="5"/>
                    </ScrollViewer>
                </GroupBox>
            </Grid>
        </Border>
    </Grid>
</Window>
