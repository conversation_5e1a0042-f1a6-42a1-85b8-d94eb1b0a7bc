﻿using CefSharp;
using CefSharp.Wpf;
using Chat.Models;
using Chat.Services;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;


namespace Chat
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        /// <summary>
        /// 应用程序配置
        /// </summary>
        public static CommandLineParser.AppConfig AppConfig { get; private set; }

        /// <summary>
        /// 当前实例ID
        /// </summary>
        public static string InstanceId { get; private set; }

        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                // 初始化日志系统
                AppLogger.Initialize();
                AppLogger.LogInfo("应用程序启动开始");

                // 解析命令行参数
                AppConfig = CommandLineParser.Parse(e.Args);
                AppLogger.LogInfo("命令行参数解析完成");

                // 如果请求显示帮助，显示帮助信息后退出
                if (AppConfig.ShowHelp)
                {
                    AppLogger.LogInfo("显示帮助信息");
                    MessageBox.Show(CommandLineParser.GetHelpText(), "命令行帮助",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    Shutdown();
                    return;
                }

                // 验证配置
                if (!CommandLineParser.ValidateConfig(AppConfig, out string errorMessage))
                {
                    AppLogger.LogError("配置验证失败: {0}", null, errorMessage);
                    MessageBox.Show($"配置错误: {errorMessage}", "错误",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    Shutdown();
                    return;
                }

                // 显示配置摘要（调试模式）
                if (AppConfig.Debug)
                {
                    var summary = CommandLineParser.GetConfigSummary(AppConfig);
                    AppLogger.LogDebug("应用程序配置摘要:\n{0}", summary);
                    System.Diagnostics.Debug.WriteLine("应用程序配置:");
                    System.Diagnostics.Debug.WriteLine(summary);
                }

                // 在解析命令行参数后初始化CefSharp（包含代理设置）
                AppLogger.LogInfo("开始初始化CefSharp");
                InitializeCefSharp();
                AppLogger.LogInfo("CefSharp初始化完成");

                base.OnStartup(e);

                // 创建并显示主窗口
                var mainWindow = new MainWindow();

                // 如果指定了启动URL，传递给主窗口
                if (!string.IsNullOrEmpty(AppConfig.StartUrl))
                {
                    AppLogger.LogInfo("加载启动URL: {0}", AppConfig.StartUrl);
                    mainWindow.LoadUrl(AppConfig.StartUrl);
                }

                AppLogger.LogInfo("主窗口显示");
                mainWindow.Show();
                AppLogger.LogInfo("应用程序启动完成");
            }
            catch (Exception ex)
            {
                AppLogger.LogFatal("应用程序启动失败", ex);
                MessageBox.Show($"应用程序启动失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        public App()
        {
            // Add Custom assembly resolver
            AppDomain.CurrentDomain.AssemblyResolve += Resolver;
            // 注意：CefSharp初始化移到OnStartup中，以便先解析命令行参数
        }

        protected override void OnExit(ExitEventArgs e)
        {
            try
            {
                AppLogger.LogInfo("应用程序正在退出，实例ID: {0}", InstanceId);

                // 关闭CefSharp
                if (Cef.IsInitialized != null && Cef.IsInitialized.Value)
                {
                    AppLogger.LogInfo("正在关闭CefSharp");
                    Cef.Shutdown();
                }

                // 清理临时文件（可选）
                CleanupInstanceFiles();

                AppLogger.Shutdown();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"应用程序退出时发生错误: {ex.Message}");
            }
            finally
            {
                base.OnExit(e);
            }
        }

        [MethodImpl(MethodImplOptions.NoInlining)]
        private static void InitializeCefSharp()
        {
            // 检查CefSharp是否已经初始化
            if (Cef.IsInitialized!=null && Cef.IsInitialized.Value)
            {
                AppLogger.LogWarning("CefSharp已经初始化，跳过重复初始化");
                return;
            }

            var settings = new CefSettings();

            // Set BrowserSubProcessPath based on app bitness at runtime
            settings.BrowserSubprocessPath = Path.Combine(
                AppDomain.CurrentDomain.SetupInformation.ApplicationBase,
                "CefSharp.BrowserSubprocess.exe"
            );

            // 支持多实例：为每个实例创建独立的用户数据目录
            var instanceId = GenerateInstanceId();
            InstanceId = instanceId;
            AppLogger.LogInfo("当前实例ID: {0}", instanceId);

            // 应用代理设置（如果有的话）
            if (AppConfig?.Proxy?.Enabled == true && AppConfig.Proxy.IsValid())
            {
                ApplyProxySettings(settings, AppConfig.Proxy);
                AppLogger.LogProxyConfig(AppConfig.Proxy.Type.ToString(), AppConfig.Proxy.Host, AppConfig.Proxy.Port,
                    !string.IsNullOrEmpty(AppConfig.Proxy.Username));
                System.Diagnostics.Debug.WriteLine($"已应用代理设置: {AppConfig.Proxy.GetDisplayName()}");
                Console.WriteLine($"代理配置: {AppConfig.Proxy.GetDisplayName()}");
            }
            else
            {
                AppLogger.LogInfo("未配置代理或代理配置无效");
                System.Diagnostics.Debug.WriteLine("未配置代理或代理配置无效");
                Console.WriteLine("未配置代理");
            }

            // 其他CefSharp设置
            settings.CefCommandLineArgs.Add("--disable-web-security");
            settings.CefCommandLineArgs.Add("--disable-features=VizDisplayCompositor");
            settings.CefCommandLineArgs.Add("--disable-gpu-vsync");

            // 设置用户数据目录（支持多实例）
            if (!string.IsNullOrEmpty(AppConfig?.UserDataDir))
            {
                // 如果指定了用户数据目录，为每个实例创建子目录
                settings.RootCachePath = Path.Combine(AppConfig.UserDataDir, instanceId);
            }
            else
            {
                // 默认用户数据目录，为每个实例创建独立目录
                var userDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "ChatApp", "CefSharp", instanceId);
                settings.RootCachePath = userDataPath;
            }

            // 设置缓存目录（支持多实例）
            if (!string.IsNullOrEmpty(AppConfig?.CacheDir))
            {
                settings.CachePath = Path.Combine(AppConfig.CacheDir, instanceId);
            }
            else
            {
                // 默认缓存目录，为每个实例创建独立目录
                settings.CachePath = Path.Combine(settings.RootCachePath, "cache");
            }

            // 确保目录存在
            Directory.CreateDirectory(settings.RootCachePath);
            Directory.CreateDirectory(settings.CachePath);

            AppLogger.LogInfo("CefSharp用户数据目录: {0}", settings.RootCachePath);
            AppLogger.LogInfo("CefSharp缓存目录: {0}", settings.CachePath);

            // 添加多实例支持的命令行参数
            settings.CefCommandLineArgs.Add("--no-sandbox");
            settings.CefCommandLineArgs.Add("--disable-extensions");
            settings.CefCommandLineArgs.Add("--disable-plugins");
            settings.CefCommandLineArgs.Add("--disable-pdf-extension");

            try
            {
                // Make sure you set performDependencyCheck false
                Cef.Initialize(settings, performDependencyCheck: false, browserProcessHandler: null);
                AppLogger.LogInfo("CefSharp初始化成功，实例ID: {0}", instanceId);
            }
            catch (Exception ex)
            {
                AppLogger.LogError("CefSharp初始化失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 生成唯一的实例ID
        /// </summary>
        private static string GenerateInstanceId()
        {
            // 使用进程ID和时间戳生成唯一ID
            var processId = System.Diagnostics.Process.GetCurrentProcess().Id;
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var randomPart = Guid.NewGuid().ToString("N").Substring(0, 8);

            return $"instance_{processId}_{timestamp}_{randomPart}";
        }

        /// <summary>
        /// 清理实例相关文件
        /// </summary>
        private static void CleanupInstanceFiles()
        {
            try
            {
                if (string.IsNullOrEmpty(InstanceId))
                    return;

                // 可选：清理临时缓存文件
                // 注意：通常不需要删除用户数据，因为用户可能希望保留会话信息
                AppLogger.LogInfo("实例文件清理完成");
            }
            catch (Exception ex)
            {
                AppLogger.LogWarning("清理实例文件时发生错误: {0}", ex.Message);
            }
        }

        /// <summary>
        /// 应用代理设置到CefSettings
        /// </summary>
        private static void ApplyProxySettings(CefSettings settings, ProxyConfig proxy)
        {
            if (!proxy.Enabled || !proxy.IsValid())
            {
                System.Diagnostics.Debug.WriteLine("代理未启用或配置无效");
                return;
            }

            // 设置代理服务器
            var proxyServer = $"{proxy.Host}:{proxy.Port}";
            string proxyUrl = "";

            switch (proxy.Type)
            {
                case ProxyType.Http:
                case ProxyType.Https:
                    proxyUrl = $"http://{proxyServer}";
                    settings.CefCommandLineArgs.Add("--proxy-server", proxyUrl);
                    break;

                case ProxyType.Socks5:
                    proxyUrl = $"socks5://{proxyServer}";
                    settings.CefCommandLineArgs.Add("--proxy-server", proxyUrl);
                    break;

                case ProxyType.Socks4:
                    proxyUrl = $"socks4://{proxyServer}";
                    settings.CefCommandLineArgs.Add("--proxy-server", proxyUrl);
                    break;
            }

            System.Diagnostics.Debug.WriteLine($"设置代理服务器: {proxyUrl}");
            Console.WriteLine($"设置代理服务器: {proxyUrl}");

            // 设置代理绕过列表
            if (!string.IsNullOrEmpty(proxy.BypassList))
            {
                settings.CefCommandLineArgs.Add("--proxy-bypass-list", proxy.BypassList);
                System.Diagnostics.Debug.WriteLine($"设置代理绕过列表: {proxy.BypassList}");
                Console.WriteLine($"设置代理绕过列表: {proxy.BypassList}");
            }

            // 输出所有CefSharp命令行参数用于调试
            System.Diagnostics.Debug.WriteLine("CefSharp命令行参数:");
            Console.WriteLine("CefSharp命令行参数:");
            foreach (var arg in settings.CefCommandLineArgs)
            {
                System.Diagnostics.Debug.WriteLine($"  {arg.Key} = {arg.Value}");
                Console.WriteLine($"  {arg.Key} = {arg.Value}");
            }
        }

        // Will attempt to load missing assembly from either x86 or x64 subdir
        // Required by CefSharp to load the unmanaged dependencies when running using AnyCPU
        private static Assembly Resolver(object sender, ResolveEventArgs args)
        {
            if (args.Name.StartsWith("CefSharp"))
            {
                string assemblyName = args.Name.Split(new[] { ',' }, 2)[0] + ".dll";
                string archSpecificPath = Path.Combine(
                    AppDomain.CurrentDomain.SetupInformation.ApplicationBase,
                    Environment.Is64BitProcess ? "x64" : "x86",
                    assemblyName
                );

                return File.Exists(archSpecificPath)
                    ? Assembly.LoadFile(archSpecificPath)
                    : null;
            }

            return null;
        }
    }
}
