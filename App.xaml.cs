﻿using CefSharp;
using CefSharp.Wpf;
using Chat.Services;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;


namespace Chat
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        /// <summary>
        /// 应用程序配置
        /// </summary>
        public static CommandLineParser.AppConfig AppConfig { get; private set; }

        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                // 解析命令行参数
                AppConfig = CommandLineParser.Parse(e.Args);

                // 如果请求显示帮助，显示帮助信息后退出
                if (AppConfig.ShowHelp)
                {
                    MessageBox.Show(CommandLineParser.GetHelpText(), "命令行帮助",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    Shutdown();
                    return;
                }

                // 验证配置
                if (!CommandLineParser.ValidateConfig(AppConfig, out string errorMessage))
                {
                    MessageBox.Show($"配置错误: {errorMessage}", "错误",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    Shutdown();
                    return;
                }

                // 显示配置摘要（调试模式）
                if (AppConfig.Debug)
                {
                    var summary = CommandLineParser.GetConfigSummary(AppConfig);
                    System.Diagnostics.Debug.WriteLine("应用程序配置:");
                    System.Diagnostics.Debug.WriteLine(summary);
                }

                base.OnStartup(e);

                // 创建并显示主窗口
                var mainWindow = new MainWindow();

                // 如果指定了启动URL，传递给主窗口
                if (!string.IsNullOrEmpty(AppConfig.StartUrl))
                {
                    mainWindow.LoadUrl(AppConfig.StartUrl);
                }

                mainWindow.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用程序启动失败: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        public App()
        {
            // Add Custom assembly resolver
            AppDomain.CurrentDomain.AssemblyResolve += Resolver;
            //Any CefSharp references have to be in another method with NonInlining
            // attribute so the assembly rolver has time to do it's thing.
            InitializeCefSharp();
        }

        [MethodImpl(MethodImplOptions.NoInlining)]
        private static void InitializeCefSharp()
        {
            var settings = new CefSettings();

            // Set BrowserSubProcessPath based on app bitness at runtime
            settings.BrowserSubprocessPath = Path.Combine(
                AppDomain.CurrentDomain.SetupInformation.ApplicationBase,
                "CefSharp.BrowserSubprocess.exe"
            );

            // 应用代理设置（如果有的话）
            if (AppConfig?.Proxy?.Enabled == true && AppConfig.Proxy.IsValid())
            {
                ApplyProxySettings(settings, AppConfig.Proxy);
                System.Diagnostics.Debug.WriteLine($"已应用代理设置: {AppConfig.Proxy.GetDisplayName()}");
            }

            // 其他CefSharp设置
            settings.CefCommandLineArgs.Add("--disable-web-security");
            settings.CefCommandLineArgs.Add("--disable-features=VizDisplayCompositor");
            settings.CefCommandLineArgs.Add("--disable-gpu-vsync");

            // 设置用户数据目录
            if (!string.IsNullOrEmpty(AppConfig?.UserDataDir))
            {
                settings.RootCachePath = AppConfig.UserDataDir;
            }
            else
            {
                var userDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "ChatApp", "CefSharp");
                settings.RootCachePath = userDataPath;
            }

            // 设置缓存目录
            if (!string.IsNullOrEmpty(AppConfig?.CacheDir))
            {
                settings.CachePath = AppConfig.CacheDir;
            }

            // Make sure you set performDependencyCheck false
            Cef.Initialize(settings, performDependencyCheck: false, browserProcessHandler: null);
        }

        /// <summary>
        /// 应用代理设置到CefSettings
        /// </summary>
        private static void ApplyProxySettings(CefSettings settings, Models.ProxyConfig proxy)
        {
            if (!proxy.Enabled || !proxy.IsValid())
                return;

            // 设置代理服务器
            var proxyServer = $"{proxy.Host}:{proxy.Port}";

            switch (proxy.Type)
            {
                case Models.ProxyType.Http:
                case Models.ProxyType.Https:
                    settings.CefCommandLineArgs.Add("--proxy-server", $"http://{proxyServer}");
                    break;

                case Models.ProxyType.Socks5:
                    settings.CefCommandLineArgs.Add("--proxy-server", $"socks5://{proxyServer}");
                    break;

                case Models.ProxyType.Socks4:
                    settings.CefCommandLineArgs.Add("--proxy-server", $"socks4://{proxyServer}");
                    break;
            }

            // 设置代理绕过列表
            if (!string.IsNullOrEmpty(proxy.BypassList))
            {
                settings.CefCommandLineArgs.Add("--proxy-bypass-list", proxy.BypassList);
            }
        }

        // Will attempt to load missing assembly from either x86 or x64 subdir
        // Required by CefSharp to load the unmanaged dependencies when running using AnyCPU
        private static Assembly Resolver(object sender, ResolveEventArgs args)
        {
            if (args.Name.StartsWith("CefSharp"))
            {
                string assemblyName = args.Name.Split(new[] { ',' }, 2)[0] + ".dll";
                string archSpecificPath = Path.Combine(
                    AppDomain.CurrentDomain.SetupInformation.ApplicationBase,
                    Environment.Is64BitProcess ? "x64" : "x86",
                    assemblyName
                );

                return File.Exists(archSpecificPath)
                    ? Assembly.LoadFile(archSpecificPath)
                    : null;
            }

            return null;
        }
    }
}
