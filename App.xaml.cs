﻿using Prism.Ioc;
using Prism.Unity;
using System.Windows;
using Chat.Services;
using Chat.ViewModels;

namespace Chat
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : PrismApplication
    {
        protected override Window CreateShell()
        {
            return Container.Resolve<MainWindow>();
        }

        protected override void RegisterTypes(IContainerRegistry containerRegistry)
        {
            // 注册服务
            containerRegistry.RegisterSingleton<IChatService, ChatService>();

            // 注册ViewModels
            containerRegistry.Register<MainWindowViewModel>();
        }
    }
}
