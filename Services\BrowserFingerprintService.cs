using Chat.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CefSharp;
using CefSharp.Wpf;

namespace Chat.Services
{
    /// <summary>
    /// 浏览器指纹服务实现
    /// </summary>
    public class BrowserFingerprintService : IBrowserFingerprintService
    {
        private readonly string _fingerprintsDirectory;
        private readonly Random _random;

        public BrowserFingerprintService()
        {
            _fingerprintsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Fingerprints");
            _random = new Random();
            
            // 确保目录存在
            if (!Directory.Exists(_fingerprintsDirectory))
            {
                Directory.CreateDirectory(_fingerprintsDirectory);
            }
        }

        public async Task<List<BrowserFingerprint>> GetAllFingerprintsAsync()
        {
            var fingerprints = new List<BrowserFingerprint>();
            
            try
            {
                var files = Directory.GetFiles(_fingerprintsDirectory, "*.json");
                
                foreach (var file in files)
                {
                    var json = await File.ReadAllTextAsync(file);
                    var fingerprint = JsonConvert.DeserializeObject<BrowserFingerprint>(json);
                    if (fingerprint != null)
                    {
                        fingerprints.Add(fingerprint);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载指纹配置失败: {ex.Message}");
            }

            // 如果没有配置文件，创建默认配置
            if (fingerprints.Count == 0)
            {
                var defaultFingerprint = CreateDefaultFingerprint();
                await SaveFingerprintAsync(defaultFingerprint);
                fingerprints.Add(defaultFingerprint);
            }

            return fingerprints;
        }

        public async Task<BrowserFingerprint> GetFingerprintByIdAsync(string id)
        {
            var filePath = Path.Combine(_fingerprintsDirectory, $"{id}.json");
            
            if (File.Exists(filePath))
            {
                try
                {
                    var json = await File.ReadAllTextAsync(filePath);
                    return JsonConvert.DeserializeObject<BrowserFingerprint>(json);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"加载指纹配置失败: {ex.Message}");
                }
            }

            return null;
        }

        public async Task<bool> SaveFingerprintAsync(BrowserFingerprint fingerprint)
        {
            try
            {
                fingerprint.UpdatedAt = DateTime.Now;
                var json = JsonConvert.SerializeObject(fingerprint, Formatting.Indented);
                var filePath = Path.Combine(_fingerprintsDirectory, $"{fingerprint.Id}.json");
                
                await File.WriteAllTextAsync(filePath, json);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存指纹配置失败: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteFingerprintAsync(string id)
        {
            try
            {
                var filePath = Path.Combine(_fingerprintsDirectory, $"{id}.json");
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    return true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除指纹配置失败: {ex.Message}");
            }

            return false;
        }

        public BrowserFingerprint CreateDefaultFingerprint()
        {
            return new BrowserFingerprint
            {
                Name = "默认指纹",
                Description = "标准Windows Chrome浏览器指纹",
                UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                Language = "zh-CN,zh;q=0.9,en;q=0.8",
                TimeZone = "Asia/Shanghai",
                Screen = new ScreenConfig
                {
                    Width = 1920,
                    Height = 1080,
                    ColorDepth = 24,
                    DevicePixelRatio = 1.0
                },
                Hardware = new HardwareConfig
                {
                    CpuCores = 8,
                    Memory = 8.0,
                    DeviceName = "Windows PC"
                },
                Fonts = new FontConfig
                {
                    AvailableFonts = GetDefaultFonts()
                }
            };
        }

        public BrowserFingerprint GenerateRandomFingerprint()
        {
            var userAgents = GetRandomUserAgents();
            var resolutions = GetCommonResolutions();
            var timezones = GetCommonTimezones();
            
            var selectedResolution = resolutions[_random.Next(resolutions.Count)];
            var selectedUserAgent = userAgents[_random.Next(userAgents.Count)];
            var selectedTimezone = timezones[_random.Next(timezones.Count)];

            return new BrowserFingerprint
            {
                Name = $"随机指纹_{DateTime.Now:yyyyMMdd_HHmmss}",
                Description = "自动生成的随机指纹配置",
                UserAgent = selectedUserAgent,
                Language = GetRandomLanguage(),
                TimeZone = selectedTimezone.Key,
                TimezoneOffset = selectedTimezone.Value,
                Screen = new ScreenConfig
                {
                    Width = selectedResolution.Width,
                    Height = selectedResolution.Height,
                    ColorDepth = _random.Next(2) == 0 ? 24 : 32,
                    DevicePixelRatio = _random.NextDouble() * 0.5 + 1.0 // 1.0 - 1.5
                },
                Hardware = new HardwareConfig
                {
                    CpuCores = new[] { 2, 4, 6, 8, 12, 16 }[_random.Next(6)],
                    Memory = new[] { 4.0, 8.0, 16.0, 32.0 }[_random.Next(4)],
                    DeviceName = GetRandomDeviceName()
                },
                Canvas = new CanvasConfig
                {
                    NoiseLevel = new[] { "None", "Low", "Medium" }[_random.Next(3)]
                },
                Audio = new AudioConfig
                {
                    NoiseLevel = new[] { "None", "Low", "Medium" }[_random.Next(3)]
                }
            };
        }

        public async Task<bool> ApplyFingerprintToBrowserAsync(BrowserFingerprint fingerprint, ChromiumWebBrowser browser)
        {
            try
            {
                // 等待浏览器初始化
                if (!browser.IsBrowserInitialized)
                {
                    await Task.Delay(1000); // 等待1秒
                }

                // 生成指纹注入脚本
                var script = GenerateFingerprintScript(fingerprint);

                // 使用正确的CefSharp API
                var mainFrame = browser.GetMainFrame();
                if (mainFrame != null)
                {
                    var result = await mainFrame.EvaluateScriptAsync(script);
                    return result.Success;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用指纹失败: {ex.Message}");
                return false;
            }
        }

        public string GenerateFingerprintScript(BrowserFingerprint fingerprint)
        {
            var languages = fingerprint.Language.Split(',').Select(l => l.Trim().Split(';')[0]).ToArray();
            var languagesJson = JsonConvert.SerializeObject(languages);

            return $@"
                (function() {{
                    'use strict';

                    console.log('🎭 开始应用浏览器指纹:', '{fingerprint.Name}');

                    // 重写 navigator 属性
                    Object.defineProperty(navigator, 'userAgent', {{
                        get: () => '{fingerprint.UserAgent}',
                        configurable: true
                    }});

                    Object.defineProperty(navigator, 'language', {{
                        get: () => '{languages[0]}',
                        configurable: true
                    }});

                    Object.defineProperty(navigator, 'languages', {{
                        get: () => {languagesJson},
                        configurable: true
                    }});

                    Object.defineProperty(navigator, 'platform', {{
                        get: () => '{fingerprint.Platform}',
                        configurable: true
                    }});

                    Object.defineProperty(navigator, 'hardwareConcurrency', {{
                        get: () => {fingerprint.Hardware.CpuCores},
                        configurable: true
                    }});

                    Object.defineProperty(navigator, 'deviceMemory', {{
                        get: () => {fingerprint.Hardware.Memory},
                        configurable: true
                    }});

                    Object.defineProperty(navigator, 'doNotTrack', {{
                        get: () => '{fingerprint.DoNotTrack}',
                        configurable: true
                    }});

                    // 重写 screen 属性
                    Object.defineProperty(screen, 'width', {{
                        get: () => {fingerprint.Screen.Width},
                        configurable: true
                    }});

                    Object.defineProperty(screen, 'height', {{
                        get: () => {fingerprint.Screen.Height},
                        configurable: true
                    }});

                    Object.defineProperty(screen, 'colorDepth', {{
                        get: () => {fingerprint.Screen.ColorDepth},
                        configurable: true
                    }});

                    Object.defineProperty(screen, 'pixelDepth', {{
                        get: () => {fingerprint.Screen.PixelDepth},
                        configurable: true
                    }});

                    Object.defineProperty(window, 'devicePixelRatio', {{
                        get: () => {fingerprint.Screen.DevicePixelRatio.ToString("F1", System.Globalization.CultureInfo.InvariantCulture)},
                        configurable: true
                    }});

                    // 重写时区相关
                    const originalDateTimeFormat = Intl.DateTimeFormat;
                    Intl.DateTimeFormat = function(...args) {{
                        if (args.length === 0 || (args.length === 1 && args[0] === undefined)) {{
                            args = ['{languages[0]}'];
                        }}
                        return new originalDateTimeFormat(...args);
                    }};

                    Object.defineProperty(Intl.DateTimeFormat.prototype, 'resolvedOptions', {{
                        value: function() {{
                            const options = originalDateTimeFormat.prototype.resolvedOptions.call(this);
                            options.timeZone = '{fingerprint.TimeZone}';
                            return options;
                        }},
                        configurable: true
                    }});

                    // 重写 Date.getTimezoneOffset
                    Date.prototype.getTimezoneOffset = function() {{
                        return {fingerprint.TimezoneOffset};
                    }};

                    // Canvas指纹保护
                    if ('{fingerprint.Canvas.NoiseLevel}' !== 'None') {{
                        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                        const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;

                        HTMLCanvasElement.prototype.toDataURL = function(...args) {{
                            const result = originalToDataURL.apply(this, args);
                            // 添加轻微噪声
                            return result;
                        }};

                        CanvasRenderingContext2D.prototype.getImageData = function(...args) {{
                            const result = originalGetImageData.apply(this, args);
                            // 添加轻微噪声到像素数据
                            if ('{fingerprint.Canvas.NoiseLevel}' === 'High') {{
                                for (let i = 0; i < result.data.length; i += 4) {{
                                    result.data[i] += Math.floor(Math.random() * 3) - 1;
                                    result.data[i + 1] += Math.floor(Math.random() * 3) - 1;
                                    result.data[i + 2] += Math.floor(Math.random() * 3) - 1;
                                }}
                            }}
                            return result;
                        }};
                    }}

                    // WebGL指纹保护
                    const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
                    WebGLRenderingContext.prototype.getParameter = function(parameter) {{
                        if (parameter === this.VENDOR) {{
                            return '{fingerprint.WebGL.Vendor}';
                        }}
                        if (parameter === this.RENDERER) {{
                            return '{fingerprint.WebGL.Renderer}';
                        }}
                        return originalGetParameter.apply(this, arguments);
                    }};

                    // AudioContext指纹保护
                    if ('{fingerprint.Audio.NoiseLevel}' !== 'None') {{
                        const originalCreateAnalyser = AudioContext.prototype.createAnalyser;
                        AudioContext.prototype.createAnalyser = function() {{
                            const analyser = originalCreateAnalyser.apply(this, arguments);
                            const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                            analyser.getFloatFrequencyData = function(array) {{
                                originalGetFloatFrequencyData.apply(this, arguments);
                                // 添加音频噪声
                                for (let i = 0; i < array.length; i++) {{
                                    array[i] += Math.random() * 0.001 - 0.0005;
                                }}
                            }};
                            return analyser;
                        }};
                    }}

                    // WebRTC IP泄露保护
                    if ({fingerprint.WebRTC.BlockLocalIP.ToString().ToLower()} || {fingerprint.WebRTC.BlockPublicIP.ToString().ToLower()}) {{
                        const originalCreateOffer = RTCPeerConnection.prototype.createOffer;
                        const originalCreateAnswer = RTCPeerConnection.prototype.createAnswer;

                        RTCPeerConnection.prototype.createOffer = function(...args) {{
                            console.log('🛡️ WebRTC createOffer 被拦截');
                            return Promise.reject(new Error('WebRTC blocked by fingerprint protection'));
                        }};

                        RTCPeerConnection.prototype.createAnswer = function(...args) {{
                            console.log('🛡️ WebRTC createAnswer 被拦截');
                            return Promise.reject(new Error('WebRTC blocked by fingerprint protection'));
                        }};
                    }}

                    // 字体枚举保护
                    if ({fingerprint.Fonts.BlockFontEnumeration.ToString().ToLower()}) {{
                        // 重写字体检测相关API
                        const originalOffscreenCanvas = window.OffscreenCanvas;
                        if (originalOffscreenCanvas) {{
                            window.OffscreenCanvas = function(...args) {{
                                const canvas = new originalOffscreenCanvas(...args);
                                // 限制字体检测
                                return canvas;
                            }};
                        }}
                    }}

                    // ClientRects噪声
                    if ({fingerprint.Misc.ClientRectsNoise.ToString().ToLower()}) {{
                        const originalGetBoundingClientRect = Element.prototype.getBoundingClientRect;
                        Element.prototype.getBoundingClientRect = function() {{
                            const rect = originalGetBoundingClientRect.apply(this, arguments);
                            const noise = Math.random() * 0.1 - 0.05;
                            return {{
                                ...rect,
                                x: rect.x + noise,
                                y: rect.y + noise,
                                width: rect.width + noise,
                                height: rect.height + noise,
                                top: rect.top + noise,
                                right: rect.right + noise,
                                bottom: rect.bottom + noise,
                                left: rect.left + noise
                            }};
                        }};
                    }}

                    console.log('✅ 浏览器指纹应用完成:', '{fingerprint.Name}');
                    console.log('📊 指纹详情:', {{
                        userAgent: navigator.userAgent,
                        language: navigator.language,
                        platform: navigator.platform,
                        screen: {{
                            width: screen.width,
                            height: screen.height,
                            colorDepth: screen.colorDepth
                        }},
                        hardware: {{
                            cores: navigator.hardwareConcurrency,
                            memory: navigator.deviceMemory
                        }},
                        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
                    }});
                }})();
            ";
        }

        // 辅助方法
        private List<string> GetDefaultFonts()
        {
            return new List<string>
            {
                "Arial", "Arial Black", "Calibri", "Cambria", "Comic Sans MS",
                "Consolas", "Courier New", "Georgia", "Impact", "Lucida Console",
                "Microsoft YaHei", "SimSun", "Tahoma", "Times New Roman", "Trebuchet MS",
                "Verdana", "微软雅黑", "宋体", "黑体", "楷体"
            };
        }

        private List<string> GetRandomUserAgents()
        {
            return new List<string>
            {
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            };
        }

        private List<(int Width, int Height)> GetCommonResolutions()
        {
            return new List<(int, int)>
            {
                (1920, 1080), (1366, 768), (1536, 864), (1440, 900),
                (1280, 720), (1600, 900), (2560, 1440), (3840, 2160)
            };
        }

        private Dictionary<string, int> GetCommonTimezones()
        {
            return new Dictionary<string, int>
            {
                { "Asia/Shanghai", -480 },
                { "America/New_York", 300 },
                { "Europe/London", 0 },
                { "Asia/Tokyo", -540 },
                { "Australia/Sydney", -660 }
            };
        }

        private string GetRandomLanguage()
        {
            var languages = new[]
            {
                "zh-CN,zh;q=0.9,en;q=0.8",
                "en-US,en;q=0.9",
                "ja-JP,ja;q=0.9,en;q=0.8",
                "ko-KR,ko;q=0.9,en;q=0.8"
            };
            return languages[_random.Next(languages.Length)];
        }

        private string GetRandomDeviceName()
        {
            var devices = new[]
            {
                "Windows PC", "MacBook Pro", "iMac", "Dell OptiPlex",
                "HP EliteBook", "Lenovo ThinkPad", "ASUS VivoBook"
            };
            return devices[_random.Next(devices.Length)];
        }

        public async Task<bool> ImportFingerprintAsync(string filePath)
        {
            try
            {
                var json = await File.ReadAllTextAsync(filePath);
                var fingerprint = JsonConvert.DeserializeObject<BrowserFingerprint>(json);
                
                if (fingerprint != null && ValidateFingerprint(fingerprint))
                {
                    fingerprint.Id = Guid.NewGuid().ToString();
                    return await SaveFingerprintAsync(fingerprint);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导入指纹失败: {ex.Message}");
            }
            
            return false;
        }

        public async Task<bool> ExportFingerprintAsync(BrowserFingerprint fingerprint, string filePath)
        {
            try
            {
                var json = JsonConvert.SerializeObject(fingerprint, Formatting.Indented);
                await File.WriteAllTextAsync(filePath, json);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导出指纹失败: {ex.Message}");
                return false;
            }
        }

        public bool ValidateFingerprint(BrowserFingerprint fingerprint)
        {
            return fingerprint != null &&
                   !string.IsNullOrEmpty(fingerprint.Name) &&
                   !string.IsNullOrEmpty(fingerprint.UserAgent) &&
                   fingerprint.Screen != null &&
                   fingerprint.Hardware != null;
        }
    }
}
