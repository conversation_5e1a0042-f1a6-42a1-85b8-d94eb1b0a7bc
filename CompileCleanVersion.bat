@echo off
chcp 65001 > nul
echo ========================================
echo Clean ChatLauncher Compilation
echo ========================================
echo.

echo Step 1: Clean previous build
if exist "ChatLauncher\bin" rmdir /s /q "ChatLauncher\bin"
if exist "ChatLauncher\obj" rmdir /s /q "ChatLauncher\obj"
echo Build folders cleaned.

echo.
echo Step 2: Verify project structure
echo Checking project files...

if exist "ChatLauncher\ChatLauncher.csproj" (
    echo ✓ ChatLauncher.csproj found
) else (
    echo ✗ ChatLauncher.csproj missing
    pause
    exit /b 1
)

if exist "ChatLauncher\MainWindow.xaml" (
    echo ✓ MainWindow.xaml found
) else (
    echo ✗ MainWindow.xaml missing
    pause
    exit /b 1
)

if exist "ChatLauncher\MainWindow.xaml.cs" (
    echo ✓ MainWindow.xaml.cs found
) else (
    echo ✗ MainWindow.xaml.cs missing
    pause
    exit /b 1
)

if exist "ChatLauncher\App.xaml" (
    echo ✓ App.xaml found
) else (
    echo ✗ App.xaml missing
    pause
    exit /b 1
)

echo.
echo Step 3: Check dependencies
if exist "packages\HandyControl.3.5.1" (
    echo ✓ HandyControl package available
) else (
    echo ⚠ HandyControl package not found
    echo   Attempting to restore packages...
    nuget restore ChatLauncher\packages.config -PackagesDirectory packages
    if %ERRORLEVEL% NEQ 0 (
        echo ⚠ Package restore failed, but continuing...
        echo   (Some HandyControl features may be limited)
    )
)

echo.
echo Step 4: Compile ChatLauncher (Clean Version)
echo Command: msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal
echo.
echo Fixed issues in this version:
echo ✓ Removed HandyControl namespace conflicts
echo ✓ Fixed MessageBox references to System.Windows.MessageBox
echo ✓ Replaced incompatible controls with standard WPF
echo ✓ Unified button styling
echo ✓ Maintained modern appearance
echo.

msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ ChatLauncher compiled successfully!
    
    if exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
        echo ✓ Output file created: ChatLauncher\bin\Debug\ChatLauncher.exe
        
        echo.
        echo Checking output directory:
        dir "ChatLauncher\bin\Debug\" /b | findstr /E ".exe .dll"
        
        echo.
        echo File size and timestamp:
        dir "ChatLauncher\bin\Debug\ChatLauncher.exe"
        
        echo.
        echo ========================================
        echo SUCCESS: Clean Compilation Complete!
        echo ========================================
        echo.
        echo This version includes:
        echo ✓ Modern HandyControl-inspired interface
        echo ✓ Fixed namespace conflicts
        echo ✓ Standard WPF MessageBox usage
        echo ✓ Compatible control implementations
        echo ✓ Professional card-based layout
        echo ✓ Chat application management
        echo ✓ Multi-instance support
        echo ✓ Configuration management
        echo ✓ Status monitoring and logging
        echo ✓ C# 7.3 compatibility
        echo ✓ .NET Framework 4.8.1 support
        echo.
        echo Features:
        echo • Detect and launch Chat.exe applications
        echo • Manage multiple chat configurations
        echo • Launch multiple instances simultaneously
        echo • Monitor running instances
        echo • View application logs
        echo • Modern, responsive interface
        echo.
        
        set /p choice=Do you want to start ChatLauncher now? (y/n): 
        if /i "%choice%"=="y" (
            echo.
            echo Starting ChatLauncher...
            echo.
            echo What to expect:
            echo 1. Modern card-based interface will appear
            echo 2. Chat application status will be detected
            echo 3. Predefined configurations will be loaded
            echo 4. You can select and launch configurations
            echo 5. Monitor running instances in real-time
            echo.
            start ChatLauncher\bin\Debug\ChatLauncher.exe
            echo.
            echo ✓ ChatLauncher started successfully!
            echo.
            echo If you encounter any issues:
            echo 1. Ensure Chat.exe exists in bin\x86\Debug\
            echo 2. Check Windows Defender/Antivirus settings
            echo 3. Verify .NET Framework 4.8.1 is installed
            echo 4. Check the logs directory for error details
        ) else (
            echo.
            echo ChatLauncher is ready to run!
            echo Execute: ChatLauncher\bin\Debug\ChatLauncher.exe
        )
    ) else (
        echo ✗ Output file not found
        echo This indicates a compilation issue despite success status.
        echo Please check the build output above for warnings.
    )
) else (
    echo.
    echo ✗ Compilation failed!
    echo.
    echo If you see errors, they might be related to:
    echo 1. Missing project references
    echo 2. XAML syntax issues
    echo 3. C# compilation errors
    echo 4. Missing dependencies
    echo 5. .NET Framework version mismatch
    echo.
    echo Please check the error messages above for specific details.
    echo.
    echo Troubleshooting steps:
    echo 1. Verify all project files are present
    echo 2. Check .NET Framework 4.8.1 installation
    echo 3. Try running: nuget restore
    echo 4. Clean and rebuild: msbuild /t:Clean then msbuild
    echo 5. Check for file permission issues
)

echo.
echo ========================================
echo Compilation Process Complete
echo ========================================
echo.
echo Thank you for using ChatLauncher!
echo For support, check the project documentation.
pause
