using Chat.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Chat.Services
{
    public class ChatService : IChatService
    {
        private List<ChatMessage> _messages;
        private List<User> _users;
        private User _currentUser;

        public ChatService()
        {
            InitializeData();
        }

        private void InitializeData()
        {
            _currentUser = new User("current_user", "Current User")
            {
                Id = 1,
                Status = UserStatus.Online
            };

            _users = new List<User>
            {
                _currentUser,
                new User("user2", "User Two") { Id = 2, Status = UserStatus.Online },
                new User("user3", "User Three") { Id = 3, Status = UserStatus.Away }
            };

            _messages = new List<ChatMessage>
            {
                new ChatMessage("Hello, welcome to the chat!", "System") { Id = 1, Type = MessageType.System },
                new ChatMessage("Hi everyone!", _currentUser.DisplayName) { Id = 2 },
                new ChatMessage("Hello there!", "User Two") { Id = 3 }
            };
        }

        public async Task<List<ChatMessage>> GetMessagesAsync()
        {
            // 模拟异步操作
            await Task.Delay(100);
            return _messages.ToList();
        }

        public async Task<bool> SendMessageAsync(ChatMessage message)
        {
            try
            {
                // 模拟异步操作
                await Task.Delay(50);
                
                message.Id = _messages.Count + 1;
                message.Timestamp = DateTime.Now;
                _messages.Add(message);
                
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<User>> GetUsersAsync()
        {
            // 模拟异步操作
            await Task.Delay(100);
            return _users.ToList();
        }

        public async Task<User> GetCurrentUserAsync()
        {
            // 模拟异步操作
            await Task.Delay(50);
            return _currentUser;
        }
    }
}
