@echo off
chcp 65001 > nul
echo ========================================
echo Compile with Selected State Visual Effects
echo ========================================
echo.

echo Step 1: Clean previous build
if exist "ChatLauncher\bin" rmdir /s /q "ChatLauncher\bin"
if exist "ChatLauncher\obj" rmdir /s /q "ChatLauncher\obj"
echo Build folders cleaned.

echo.
echo Step 2: Selected state visual improvements added:
echo ✓ Selected border color: #FF007ACC (Primary Blue)
echo ✓ Selected background: Semi-transparent blue (#1A007ACC)
echo ✓ Hover border color: #FF4A9EFF (Light Blue)
echo ✓ Hover background: Semi-transparent light blue (#0A4A9EFF)
echo ✓ Selected indicator: ✓ checkmark icon
echo ✓ Enhanced shadow effects for selected items
echo ✓ Smooth visual transitions

echo.
echo Step 3: Compile ChatLauncher with selected state effects
echo Command: msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal
echo.

msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ ChatLauncher compiled successfully with selected state effects!
    
    if exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
        echo ✓ Output file created: ChatLauncher\bin\Debug\ChatLauncher.exe
        
        echo.
        echo ========================================
        echo SUCCESS: Selected State Visual Effects!
        echo ========================================
        echo.
        echo 🎨 Visual State Improvements:
        echo.
        echo 📋 Configuration List States:
        echo    • Normal: Default border and background
        echo    • Hover: Light blue border (#FF4A9EFF) + subtle background
        echo    • Selected: Primary blue border (#FF007ACC) + blue background
        echo    • Selected indicator: ✓ checkmark appears on selected items
        echo.
        echo 🎯 Color Scheme:
        echo    • Primary Blue: #FF007ACC (selected border)
        echo    • Light Blue: #FF4A9EFF (hover border)
        echo    • Selected Background: Semi-transparent blue
        echo    • Hover Background: Very light blue tint
        echo.
        echo 💫 Enhanced Effects:
        echo    • Border thickness: 2px for better visibility
        echo    • Drop shadow: Enhanced for selected items
        echo    • Corner radius: 6px for modern appearance
        echo    • Smooth visual feedback
        echo.
        echo 🔍 User Experience:
        echo    • Clear visual distinction between states
        echo    • Immediate feedback on selection
        echo    • Professional, modern appearance
        echo    • Consistent with overall design language
        echo.
        
        set /p choice=Launch ChatLauncher to test selected state effects? (y/n): 
        if /i "%choice%"=="y" (
            echo.
            echo Starting ChatLauncher with selected state visual effects...
            echo.
            echo 🎉 Test the following interactions:
            echo.
            echo 1. 🖱️ Hover over configuration items:
            echo    • Should see light blue border appear
            echo    • Subtle background color change
            echo    • Enhanced shadow effect
            echo.
            echo 2. 🖱️ Click to select configuration items:
            echo    • Should see primary blue border
            echo    • Blue background tint
            echo    • ✓ checkmark indicator appears
            echo    • Selected item stands out clearly
            echo.
            echo 3. 🔄 Switch between different configurations:
            echo    • Previous selection should return to normal
            echo    • New selection should highlight immediately
            echo    • Smooth visual transitions
            echo.
            echo 4. 📱 Overall visual consistency:
            echo    • Selected state matches app theme
            echo    • Professional appearance
            echo    • Clear visual hierarchy
            echo.
            start ChatLauncher\bin\Debug\ChatLauncher.exe
            echo.
            echo ✅ ChatLauncher launched with selected state effects!
            echo.
            echo 🎯 Expected Behavior:
            echo • Configuration list items now have clear visual states
            echo • Selected items are highlighted with blue border and background
            echo • Hover effects provide immediate visual feedback
            echo • ✓ checkmark indicates currently selected configuration
            echo • Professional, modern interface appearance
        ) else (
            echo.
            echo ChatLauncher with selected state effects is ready!
            echo Execute: ChatLauncher\bin\Debug\ChatLauncher.exe
        )
    ) else (
        echo ✗ Output file not found
        echo This indicates a compilation issue.
    )
) else (
    echo.
    echo ✗ Compilation failed!
    echo Please check the error messages above for specific issues.
)

echo.
echo ========================================
echo Selected State Implementation Summary
echo ========================================
echo.
echo 🎨 Visual States Implemented:
echo.
echo 1. 📋 Normal State:
echo    • Default border color from theme
echo    • Standard background
echo    • Basic drop shadow
echo.
echo 2. 🖱️ Hover State:
echo    • Light blue border (#FF4A9EFF)
echo    • Subtle background tint (#0A4A9EFF)
echo    • Enhanced shadow effect
echo.
echo 3. ✅ Selected State:
echo    • Primary blue border (#FF007ACC)
echo    • Blue background tint (#1A007ACC)
echo    • ✓ checkmark indicator
echo    • Prominent visual distinction
echo.
echo 🔧 Technical Implementation:
echo    • Custom ListBoxItem template
echo    • Trigger-based state management
echo    • Consistent color scheme
echo    • Smooth visual transitions
echo    • Professional styling
echo.
echo 🎯 User Benefits:
echo    • Clear visual feedback
echo    • Easy identification of selected items
echo    • Improved usability
echo    • Modern, professional appearance
echo    • Consistent design language
echo.
echo The configuration list now provides excellent visual feedback! 🎉
pause
