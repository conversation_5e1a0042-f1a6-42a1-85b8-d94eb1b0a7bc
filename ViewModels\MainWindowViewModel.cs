using Prism.Mvvm;
using Prism.Commands;
using System.Windows.Input;
using Chat.Services;
using Chat.Models;
using System.Collections.ObjectModel;
using System.Threading.Tasks;

namespace Chat.ViewModels
{
    public class MainWindowViewModel : BindableBase
    {
        private readonly IChatService _chatService;
        private string _title = "Chat Application";
        private string _browserUrl = "https://www.psvmc.cn";
        private ObservableCollection<ChatMessage> _messages;
        private ObservableCollection<User> _users;

        public string Title
        {
            get { return _title; }
            set { SetProperty(ref _title, value); }
        }

        public string BrowserUrl
        {
            get { return _browserUrl; }
            set { SetProperty(ref _browserUrl, value); }
        }

        public ObservableCollection<ChatMessage> Messages
        {
            get { return _messages; }
            set { SetProperty(ref _messages, value); }
        }

        public ObservableCollection<User> Users
        {
            get { return _users; }
            set { SetProperty(ref _users, value); }
        }

        public ICommand LoadUrlCommand { get; private set; }
        public ICommand LoadDataCommand { get; private set; }

        public MainWindowViewModel(IChatService chatService)
        {
            _chatService = chatService;
            Messages = new ObservableCollection<ChatMessage>();
            Users = new ObservableCollection<User>();

            LoadUrlCommand = new DelegateCommand<string>(LoadUrl);
            LoadDataCommand = new DelegateCommand(async () => await LoadDataAsync());

            // 初始化时加载数据
            _ = LoadDataAsync();
        }

        private void LoadUrl(string url)
        {
            if (!string.IsNullOrEmpty(url))
            {
                BrowserUrl = url;
            }
        }

        private async Task LoadDataAsync()
        {
            try
            {
                var messages = await _chatService.GetMessagesAsync();
                var users = await _chatService.GetUsersAsync();

                Messages.Clear();
                foreach (var message in messages)
                {
                    Messages.Add(message);
                }

                Users.Clear();
                foreach (var user in users)
                {
                    Users.Add(user);
                }
            }
            catch (System.Exception ex)
            {
                // 处理异常，可以使用日志记录或显示错误消息
                System.Diagnostics.Debug.WriteLine($"Error loading data: {ex.Message}");
            }
        }
    }
}
