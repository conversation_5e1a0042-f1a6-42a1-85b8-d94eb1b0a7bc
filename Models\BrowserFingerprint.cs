using System;
using System.Collections.Generic;

namespace Chat.Models
{
    /// <summary>
    /// 浏览器指纹配置模型
    /// </summary>
    public class BrowserFingerprint
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = "默认指纹";
        public string Description { get; set; } = "";
        public bool IsEnabled { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // 网络配置
        public ProxyConfig Proxy { get; set; } = new ProxyConfig();

        // 浏览器基础信息
        public string UserAgent { get; set; } = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";
        public string Language { get; set; } = "zh-CN,zh;q=0.9,en;q=0.8";
        public string AcceptLanguage { get; set; } = "zh-CN,zh;q=0.9,en;q=0.8";
        public string Platform { get; set; } = "Win32";
        public string DoNotTrack { get; set; } = "1";

        // 地理位置和时区
        public GeolocationConfig Geolocation { get; set; } = new GeolocationConfig();
        public string TimeZone { get; set; } = "Asia/Shanghai";
        public int TimezoneOffset { get; set; } = -480; // 分钟

        // 屏幕和显示
        public ScreenConfig Screen { get; set; } = new ScreenConfig();
        public ViewportConfig Viewport { get; set; } = new ViewportConfig();

        // 硬件信息
        public HardwareConfig Hardware { get; set; } = new HardwareConfig();

        // WebRTC配置
        public WebRTCConfig WebRTC { get; set; } = new WebRTCConfig();

        // Canvas指纹
        public CanvasConfig Canvas { get; set; } = new CanvasConfig();

        // WebGL配置
        public WebGLConfig WebGL { get; set; } = new WebGLConfig();

        // WebGPU配置
        public WebGPUConfig WebGPU { get; set; } = new WebGPUConfig();

        // 音频配置
        public AudioConfig Audio { get; set; } = new AudioConfig();

        // 媒体设备
        public MediaDevicesConfig MediaDevices { get; set; } = new MediaDevicesConfig();

        // 字体配置
        public FontConfig Fonts { get; set; } = new FontConfig();

        // 其他配置
        public MiscConfig Misc { get; set; } = new MiscConfig();
    }



    /// <summary>
    /// 地理位置配置
    /// </summary>
    public class GeolocationConfig
    {
        public bool Enabled { get; set; } = false;
        public double Latitude { get; set; } = 39.9042; // 北京
        public double Longitude { get; set; } = 116.4074;
        public double Accuracy { get; set; } = 10.0;
    }

    /// <summary>
    /// 屏幕配置
    /// </summary>
    public class ScreenConfig
    {
        public int Width { get; set; } = 1920;
        public int Height { get; set; } = 1080;
        public int ColorDepth { get; set; } = 24;
        public int PixelDepth { get; set; } = 24;
        public double DevicePixelRatio { get; set; } = 1.0;
    }

    /// <summary>
    /// 视口配置
    /// </summary>
    public class ViewportConfig
    {
        public int Width { get; set; } = 1920;
        public int Height { get; set; } = 969;
    }

    /// <summary>
    /// 硬件配置
    /// </summary>
    public class HardwareConfig
    {
        public int CpuCores { get; set; } = 8;
        public string CpuClass { get; set; } = "";
        public double Memory { get; set; } = 8.0; // GB
        public string DeviceName { get; set; } = "PC";
        public string Vendor { get; set; } = "Google Inc.";
        public string Renderer { get; set; } = "ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)";
    }

    /// <summary>
    /// WebRTC配置
    /// </summary>
    public class WebRTCConfig
    {
        public bool Enabled { get; set; } = true;
        public bool BlockLocalIP { get; set; } = false;
        public bool BlockPublicIP { get; set; } = false;
        public string LocalIP { get; set; } = "*************";
        public string PublicIP { get; set; } = "";
    }

    /// <summary>
    /// Canvas配置
    /// </summary>
    public class CanvasConfig
    {
        public bool Enabled { get; set; } = true;
        public string NoiseLevel { get; set; } = "Low"; // None, Low, Medium, High
        public string CustomFingerprint { get; set; } = "";
    }

    /// <summary>
    /// WebGL配置
    /// </summary>
    public class WebGLConfig
    {
        public bool Enabled { get; set; } = true;
        public string Vendor { get; set; } = "Google Inc. (Intel)";
        public string Renderer { get; set; } = "ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)";
        public string Version { get; set; } = "WebGL 1.0 (OpenGL ES 2.0 Chromium)";
        public string ShadingLanguageVersion { get; set; } = "WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)";
        public List<string> Extensions { get; set; } = new List<string>();
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// WebGPU配置
    /// </summary>
    public class WebGPUConfig
    {
        public bool Enabled { get; set; } = false;
        public string AdapterName { get; set; } = "";
        public string VendorID { get; set; } = "";
        public string DeviceID { get; set; } = "";
    }

    /// <summary>
    /// 音频配置
    /// </summary>
    public class AudioConfig
    {
        public bool Enabled { get; set; } = true;
        public string NoiseLevel { get; set; } = "Low";
        public double SampleRate { get; set; } = 44100;
        public int MaxChannelCount { get; set; } = 2;
        public double BaseLatency { get; set; } = 0.01;
    }

    /// <summary>
    /// 媒体设备配置
    /// </summary>
    public class MediaDevicesConfig
    {
        public bool Enabled { get; set; } = true;
        public List<MediaDeviceInfo> AudioInputs { get; set; } = new List<MediaDeviceInfo>();
        public List<MediaDeviceInfo> VideoInputs { get; set; } = new List<MediaDeviceInfo>();
        public List<MediaDeviceInfo> AudioOutputs { get; set; } = new List<MediaDeviceInfo>();
    }

    /// <summary>
    /// 媒体设备信息
    /// </summary>
    public class MediaDeviceInfo
    {
        public string DeviceId { get; set; } = "";
        public string Kind { get; set; } = ""; // audioinput, videoinput, audiooutput
        public string Label { get; set; } = "";
        public string GroupId { get; set; } = "";
    }

    /// <summary>
    /// 字体配置
    /// </summary>
    public class FontConfig
    {
        public bool Enabled { get; set; } = true;
        public List<string> AvailableFonts { get; set; } = new List<string>();
        public bool BlockFontEnumeration { get; set; } = false;
    }

    /// <summary>
    /// 其他配置
    /// </summary>
    public class MiscConfig
    {
        public bool SpeechSynthesisEnabled { get; set; } = true;
        public List<string> SpeechVoices { get; set; } = new List<string>();
        public bool ClientRectsNoise { get; set; } = false;
        public string NoiseLevel { get; set; } = "Low";
        public Dictionary<string, object> CustomProperties { get; set; } = new Dictionary<string, object>();
    }
}
