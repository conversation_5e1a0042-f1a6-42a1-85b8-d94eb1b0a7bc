<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Info"
      internalLogFile="logs/internal-nlog.txt">

  <!-- 定义变量 -->
  <variable name="logDirectory" value="logs"/>
  <variable name="applicationName" value="ChatApp"/>
  
  <!-- 定义日志输出目标 -->
  <targets>
    
    <!-- 控制台输出 -->
    <target xsi:type="Console" 
            name="console"
            layout="${longdate} ${level:uppercase=true:padding=-5} ${logger:shortName=true} ${message} ${exception:format=tostring}" />
    
    <!-- 调试输出 -->
    <target xsi:type="Debugger" 
            name="debugger"
            layout="${time} ${level:uppercase=true:padding=-5} ${logger:shortName=true} ${message}" />
    
    <!-- 所有日志文件 -->
    <target xsi:type="File" 
            name="allfile"
            fileName="${logDirectory}/all-${shortdate}.log"
            layout="${longdate} ${level:uppercase=true:padding=-5} ${logger} ${message} ${exception:format=tostring}"
            archiveFileName="${logDirectory}/archives/all-{#}.log"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="30"
            concurrentWrites="true"
            keepFileOpen="false" />
    
    <!-- 错误日志文件 -->
    <target xsi:type="File" 
            name="errorfile"
            fileName="${logDirectory}/error-${shortdate}.log"
            layout="${longdate} ${level:uppercase=true:padding=-5} ${logger} ${message} ${exception:format=tostring}"
            archiveFileName="${logDirectory}/archives/error-{#}.log"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="30"
            concurrentWrites="true"
            keepFileOpen="false" />
    
    <!-- HTTP请求日志文件 -->
    <target xsi:type="File" 
            name="httpfile"
            fileName="${logDirectory}/http-${shortdate}.log"
            layout="${longdate} [${level:uppercase=true}] ${message}"
            archiveFileName="${logDirectory}/archives/http-{#}.log"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="30"
            concurrentWrites="true"
            keepFileOpen="false" />
    
    <!-- TCP请求日志文件 -->
    <target xsi:type="File" 
            name="tcpfile"
            fileName="${logDirectory}/tcp-${shortdate}.log"
            layout="${longdate} [${level:uppercase=true}] ${message}"
            archiveFileName="${logDirectory}/archives/tcp-{#}.log"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="30"
            concurrentWrites="true"
            keepFileOpen="false" />
    
    <!-- 文件操作日志文件 -->
    <target xsi:type="File" 
            name="fileopsfile"
            fileName="${logDirectory}/fileops-${shortdate}.log"
            layout="${longdate} [${level:uppercase=true}] ${message}"
            archiveFileName="${logDirectory}/archives/fileops-{#}.log"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="30"
            concurrentWrites="true"
            keepFileOpen="false" />
    
    <!-- 数据库操作日志文件 -->
    <target xsi:type="File" 
            name="dbfile"
            fileName="${logDirectory}/database-${shortdate}.log"
            layout="${longdate} [${level:uppercase=true}] ${message}"
            archiveFileName="${logDirectory}/archives/database-{#}.log"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="30"
            concurrentWrites="true"
            keepFileOpen="false" />
    
    <!-- 代理日志文件 -->
    <target xsi:type="File" 
            name="proxyfile"
            fileName="${logDirectory}/proxy-${shortdate}.log"
            layout="${longdate} [${level:uppercase=true}] ${message}"
            archiveFileName="${logDirectory}/archives/proxy-{#}.log"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="30"
            concurrentWrites="true"
            keepFileOpen="false" />
    
    <!-- 聊天提取日志文件 -->
    <target xsi:type="File" 
            name="chatfile"
            fileName="${logDirectory}/chat-${shortdate}.log"
            layout="${longdate} [${level:uppercase=true}] ${message}"
            archiveFileName="${logDirectory}/archives/chat-{#}.log"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="30"
            concurrentWrites="true"
            keepFileOpen="false" />
    
  </targets>

  <!-- 定义日志规则 -->
  <rules>
    
    <!-- 所有日志级别输出到控制台（仅Debug模式） -->
    <logger name="*" minlevel="Debug" writeTo="console" />
    
    <!-- 所有日志级别输出到调试器 -->
    <logger name="*" minlevel="Trace" writeTo="debugger" />
    
    <!-- 所有日志输出到总日志文件 -->
    <logger name="*" minlevel="Info" writeTo="allfile" />
    
    <!-- 错误和致命错误输出到错误日志文件 -->
    <logger name="*" minlevel="Error" writeTo="errorfile" />
    
    <!-- HTTP请求日志 -->
    <logger name="Chat.Http.*" minlevel="Debug" writeTo="httpfile" final="true" />
    
    <!-- TCP请求日志 -->
    <logger name="Chat.Tcp.*" minlevel="Debug" writeTo="tcpfile" final="true" />
    
    <!-- 文件操作日志 -->
    <logger name="Chat.FileOps.*" minlevel="Debug" writeTo="fileopsfile" final="true" />
    
    <!-- 数据库操作日志 -->
    <logger name="Chat.Database.*" minlevel="Debug" writeTo="dbfile" final="true" />
    
    <!-- 代理操作日志 -->
    <logger name="Chat.Proxy.*" minlevel="Debug" writeTo="proxyfile" final="true" />
    
    <!-- 聊天提取日志 -->
    <logger name="Chat.ChatExtraction.*" minlevel="Debug" writeTo="chatfile" final="true" />
    
  </rules>
  
</nlog>
