@echo off
echo ========================================
echo 代理调试测试工具
echo ========================================
echo.

echo 请选择测试场景：
echo 1. HTTP代理测试 (127.0.0.1:8080)
echo 2. HTTP代理测试 (127.0.0.1:8888) 
echo 3. SOCKS5代理测试 (127.0.0.1:1080)
echo 4. 自定义代理测试
echo 5. 无代理测试（对比）
echo 6. 退出
echo.

set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" goto http_8080
if "%choice%"=="2" goto http_8888
if "%choice%"=="3" goto socks5_1080
if "%choice%"=="4" goto custom_proxy
if "%choice%"=="5" goto no_proxy
if "%choice%"=="6" goto exit

echo 无效选择，退出...
goto exit

:http_8080
echo.
echo 启动应用（HTTP代理：127.0.0.1:8080）...
echo 请观察控制台输出的代理配置信息
echo.
Chat.exe --proxy http://127.0.0.1:8080 --url https://www.browserscan.net/zh --debug
goto end

:http_8888
echo.
echo 启动应用（HTTP代理：127.0.0.1:8888）...
echo 请观察控制台输出的代理配置信息
echo.
Chat.exe --proxy http://127.0.0.1:8888 --url https://www.browserscan.net/zh --debug
goto end

:socks5_1080
echo.
echo 启动应用（SOCKS5代理：127.0.0.1:1080）...
echo 请观察控制台输出的代理配置信息
echo.
Chat.exe --proxy socks5://127.0.0.1:1080 --url https://www.browserscan.net/zh --debug
goto end

:custom_proxy
echo.
echo 请输入自定义代理URL（例如：http://127.0.0.1:8080）
set /p proxy_url=代理URL: 
if "%proxy_url%"=="" goto custom_proxy
echo.
echo 启动应用（自定义代理：%proxy_url%）...
echo 请观察控制台输出的代理配置信息
echo.
Chat.exe --proxy %proxy_url% --url https://www.browserscan.net/zh --debug
goto end

:no_proxy
echo.
echo 启动应用（无代理）...
echo 用于对比测试
echo.
Chat.exe --url https://www.browserscan.net/zh --debug
goto end

:exit
echo 退出测试工具
goto end

:end
echo.
echo 测试完成，按任意键退出...
pause > nul
