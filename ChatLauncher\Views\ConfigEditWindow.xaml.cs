using System;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using ChatLauncher.Models;
using ChatLauncher.Services;

namespace ChatLauncher.Views
{
    /// <summary>
    /// 配置编辑窗口
    /// </summary>
    public partial class ConfigEditWindow : HandyControl.Controls.Window
    {
        private LaunchConfig _config;
        private readonly bool _isNewConfig;
        private readonly ChatAppLauncher _launcher;

        public LaunchConfig Config => _config;
        public bool DialogResult { get; private set; }

        /// <summary>
        /// 新建配置构造函数
        /// </summary>
        public ConfigEditWindow()
        {
            InitializeComponent();
            _isNewConfig = true;
            _config = new LaunchConfig
            {
                Name = "",
                Icon = "🚀",
                CreatedTime = DateTime.Now,
                IsDefault = false
            };
            _launcher = new ChatAppLauncher();
            
            WindowTitle.Text = "新建配置";
            TitleIcon.Text = "➕";
            LoadConfigToForm();
        }

        /// <summary>
        /// 编辑配置构造函数
        /// </summary>
        public ConfigEditWindow(LaunchConfig config)
        {
            InitializeComponent();
            _isNewConfig = false;
            _config = config.Clone();
            _launcher = new ChatAppLauncher();
            
            WindowTitle.Text = "编辑配置";
            TitleIcon.Text = "✏️";
            LoadConfigToForm();
        }

        /// <summary>
        /// 将配置加载到表单
        /// </summary>
        private void LoadConfigToForm()
        {
            NameTextBox.Text = _config.Name ?? "";
            DescriptionTextBox.Text = _config.Description ?? "";
            IconTextBox.Text = _config.Icon ?? "🚀";
            UrlTextBox.Text = _config.Url ?? "";
            ProxyTextBox.Text = _config.Proxy ?? "";
            WidthTextBox.Text = _config.Width?.ToString() ?? "";
            HeightTextBox.Text = _config.Height?.ToString() ?? "";
            FullscreenCheckBox.IsChecked = _config.Fullscreen;
            ExtraArgsTextBox.Text = _config.ExtraArgs ?? "";
            DebugCheckBox.IsChecked = _config.Debug;
            UserDataDirTextBox.Text = _config.UserDataDir ?? "";
            CacheDirTextBox.Text = _config.CacheDir ?? "";
            IsFavoriteCheckBox.IsChecked = _config.IsFavorite;
        }

        /// <summary>
        /// 从表单保存配置
        /// </summary>
        private bool SaveConfigFromForm()
        {
            try
            {
                _config.Name = NameTextBox.Text?.Trim();
                _config.Description = DescriptionTextBox.Text?.Trim();
                _config.Icon = IconTextBox.Text?.Trim();
                _config.Url = UrlTextBox.Text?.Trim();
                _config.Proxy = ProxyTextBox.Text?.Trim();
                _config.Fullscreen = FullscreenCheckBox.IsChecked == true;
                _config.ExtraArgs = ExtraArgsTextBox.Text?.Trim();
                _config.Debug = DebugCheckBox.IsChecked == true;
                _config.UserDataDir = UserDataDirTextBox.Text?.Trim();
                _config.CacheDir = CacheDirTextBox.Text?.Trim();
                _config.IsFavorite = IsFavoriteCheckBox.IsChecked == true;

                // 解析窗口大小
                if (int.TryParse(WidthTextBox.Text, out int width) && width > 0)
                {
                    _config.Width = width;
                }
                else
                {
                    _config.Width = null;
                }

                if (int.TryParse(HeightTextBox.Text, out int height) && height > 0)
                {
                    _config.Height = height;
                }
                else
                {
                    _config.Height = null;
                }

                // 验证配置
                if (!_config.IsValid(out string errorMessage))
                {
                    MessageBox.Show(errorMessage, "配置验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存配置时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// 图标选择改变事件
        /// </summary>
        private void IconComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IconComboBox.SelectedItem is ComboBoxItem item)
            {
                var content = item.Content.ToString();
                if (content.Length > 0)
                {
                    var icon = content.Split(' ')[0];
                    IconTextBox.Text = icon;
                }
            }
        }

        /// <summary>
        /// 浏览用户数据目录
        /// </summary>
        private void BrowseUserDataDir_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = "选择用户数据目录",
                ShowNewFolderButton = true
            };

            if (!string.IsNullOrEmpty(UserDataDirTextBox.Text))
            {
                dialog.SelectedPath = UserDataDirTextBox.Text;
            }

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                UserDataDirTextBox.Text = dialog.SelectedPath;
            }
        }

        /// <summary>
        /// 浏览缓存目录
        /// </summary>
        private void BrowseCacheDir_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = "选择缓存目录",
                ShowNewFolderButton = true
            };

            if (!string.IsNullOrEmpty(CacheDirTextBox.Text))
            {
                dialog.SelectedPath = CacheDirTextBox.Text;
            }

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                CacheDirTextBox.Text = dialog.SelectedPath;
            }
        }

        /// <summary>
        /// 测试配置
        /// </summary>
        private void TestButton_Click(object sender, RoutedEventArgs e)
        {
            if (!SaveConfigFromForm())
            {
                return;
            }

            try
            {
                if (_launcher.TestLaunch(_config, out string commandLine))
                {
                    MessageBox.Show($"配置测试成功！\n\n命令行预览：\n{commandLine}", 
                        "测试成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show($"配置测试失败：\n{commandLine}", 
                        "测试失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"测试配置时发生错误：{ex.Message}", 
                    "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 保存配置
        /// </summary>
        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (SaveConfigFromForm())
            {
                DialogResult = true;
                Close();
            }
        }

        /// <summary>
        /// 取消编辑
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
