# 🚀 聊天应用启动器 (ChatLauncher)

## 📋 功能概述

聊天应用启动器是一个专门为Chat聊天集成应用设计的图形化启动工具，提供了比批处理脚本更加友好和强大的功能。

## ✨ 主要功能

### 1. **预定义配置**
- 🚀 默认启动
- 💬 WhatsApp Web
- ✈️ Telegram Web  
- 📘 Facebook Messenger
- 🎮 Discord Web
- 🔍 浏览器指纹测试
- 🌐 HTTP代理测试
- 🔒 SOCKS5代理测试

### 2. **启动选项**
- ✅ 单实例启动
- ✅ 多实例启动（可指定数量）
- ✅ 测试模式（验证配置不实际启动）
- ✅ 命令行预览
- ✅ 实时状态监控

### 3. **配置管理**
- ✅ 搜索和过滤配置
- ✅ 收藏配置
- ✅ 使用统计
- ✅ 配置验证
- 🔧 新建/编辑/删除配置（开发中）

### 4. **实例管理**
- ✅ 查看运行中的实例数量
- ✅ 关闭所有实例
- ✅ 实例状态监控
- ✅ 启动日志记录

## 🎯 使用方法

### 启动启动器
1. 编译ChatLauncher项目
2. 运行`ChatLauncher.exe`
3. 确保Chat.exe在正确位置（`../bin/x86/Debug/Chat.exe`）

### 基本操作
1. **选择配置** - 在左侧列表中选择要使用的配置
2. **查看详情** - 右侧显示配置详情和命令行预览
3. **设置选项** - 选择单实例/多实例启动
4. **启动应用** - 点击"🚀 启动应用"按钮

### 多实例启动
1. 勾选"启动多个实例"
2. 设置实例数量
3. 点击启动按钮
4. 每个实例会有独立的用户数据目录

### 测试模式
1. 勾选"测试模式"
2. 点击启动按钮
3. 查看配置验证结果和命令行预览

## 🔧 技术特性

### 自动发现Chat应用
- 自动搜索多个可能的Chat.exe位置
- 显示应用版本和路径信息
- 实时检查应用可用性

### 智能配置管理
- 配置验证和错误提示
- 使用统计和收藏功能
- 搜索和过滤功能

### 实例监控
- 实时监控运行中的实例
- 批量关闭所有实例
- 启动日志记录

### 用户友好界面
- 现代化WPF界面
- 直观的图标和描述
- 实时状态更新

## 📁 文件结构

```
ChatLauncher/
├── Models/
│   └── LaunchConfig.cs          # 启动配置模型
├── Services/
│   └── ChatAppLauncher.cs       # 启动器服务
├── MainWindow.xaml              # 主窗口界面
├── MainWindow.xaml.cs           # 主窗口逻辑
├── App.xaml                     # 应用程序定义
├── App.xaml.cs                  # 应用程序逻辑
└── Properties/                  # 程序集信息
```

## 🎨 界面预览

### 主界面
- **左侧**：配置列表，支持搜索和过滤
- **右侧**：配置详情、启动选项、命令行预览
- **顶部**：标题栏和工具按钮
- **底部**：状态栏和实例管理

### 配置列表
- 显示配置图标、名称、描述
- 收藏配置标星显示
- 使用次数统计
- 智能排序（收藏 > 使用次数 > 名称）

## 🚀 优势对比

### vs 批处理脚本
| 功能 | 批处理脚本 | ChatLauncher |
|------|------------|--------------|
| 用户界面 | 命令行 | 图形化界面 |
| 配置管理 | 硬编码 | 动态配置 |
| 错误处理 | 基础 | 完善 |
| 状态监控 | 无 | 实时监控 |
| 多实例支持 | 基础 | 高级 |
| 使用统计 | 无 | 完整统计 |
| 搜索过滤 | 无 | 支持 |
| 扩展性 | 差 | 优秀 |

## 🔮 未来功能

### 计划中的功能
- 🔧 配置编辑器
- 💾 配置导入/导出
- 🎨 主题切换
- 📊 使用统计图表
- 🔔 启动通知
- 🌐 在线配置同步
- 📱 移动端支持

### 高级功能
- 🤖 自动更新检查
- 🔐 配置加密
- 📝 启动脚本支持
- 🎯 性能监控
- 🔄 配置备份恢复

## 🛠️ 开发说明

### 编译要求
- .NET Framework 4.8.1
- Visual Studio 2019+
- WPF支持

### 依赖项目
- 需要Chat主项目在相对路径`../`
- 自动检测Chat.exe位置

### 扩展开发
- 模块化设计，易于扩展
- 清晰的服务分离
- 完整的错误处理

## 📋 使用建议

1. **首次使用** - 先编译Chat项目，确保可执行文件存在
2. **配置选择** - 根据需要选择合适的预定义配置
3. **多实例使用** - 合理控制实例数量，避免资源消耗过大
4. **日志查看** - 定期查看启动日志，了解使用情况
5. **实例管理** - 及时关闭不需要的实例，释放系统资源

## 🎉 总结

ChatLauncher提供了一个现代化、用户友好的方式来管理和启动Chat聊天集成应用，相比传统的批处理脚本，它具有更好的用户体验、更强的功能和更高的可扩展性。

无论是日常使用还是开发测试，ChatLauncher都能显著提升您的工作效率！🚀
