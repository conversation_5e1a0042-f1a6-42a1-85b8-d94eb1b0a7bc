using Chat.Models;
using Chat.Services;
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace Chat.Controls
{
    public partial class ChatPlatformTab : UserControl
    {
        private WebChatExtractor _extractor;
        private string _platformName;
        private string _platformUrl;
        private int _messageCount = 0;
        private bool _isExtracting = false;

        public event EventHandler<ChatMessage> MessageReceived;

        public ChatPlatformTab()
        {
            InitializeComponent();
            InitializeBrowser();
        }

        public void Initialize(string platformName, string platformUrl, string iconPath = null)
        {
            _platformName = platformName;
            _platformUrl = platformUrl;
            
            PlatformName.Text = platformName;
            
            if (!string.IsNullOrEmpty(iconPath))
            {
                // 设置平台图标
                // PlatformIcon.Source = new BitmapImage(new Uri(iconPath));
            }
            
            // 导航到平台URL
            WebBrowser.Address = platformUrl;
            
            // 初始化提取器
            _extractor = new WebChatExtractor(WebBrowser, platformName);
            _extractor.MessageExtracted += OnMessageExtracted;
            _extractor.StatusChanged += OnStatusChanged;
        }

        private void InitializeBrowser()
        {
            // 浏览器事件处理
            WebBrowser.LoadingStateChanged += (sender, e) =>
            {
                if (!e.IsLoading)
                {
                    UpdateStatus("已加载", Colors.Green);
                }
                else
                {
                    UpdateStatus("加载中...", Colors.Orange);
                }
            };

            WebBrowser.LoadError += (sender, e) =>
            {
                UpdateStatus("加载失败", Colors.Red);
            };
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            // 使用重新加载当前地址的方式
            var currentUrl = WebBrowser.Address;
            if (!string.IsNullOrEmpty(currentUrl))
            {
                WebBrowser.LoadUrl(currentUrl);
            }
            _messageCount = 0;
            UpdateMessageCount();
        }

        private async void ExtractButton_Click(object sender, RoutedEventArgs e)
        {
            if (!_isExtracting)
            {
                // 开始监听
                var success = await _extractor.InjectExtractionScriptAsync();
                if (success)
                {
                    _isExtracting = true;
                    ExtractButton.Content = "停止监听";
                    UpdateStatus("监听中", Colors.LimeGreen);
                }
                else
                {
                    UpdateStatus("启动失败", Colors.Red);
                }
            }
            else
            {
                // 停止监听
                _extractor?.StopExtraction();
                _isExtracting = false;
                ExtractButton.Content = "开始监听";
                UpdateStatus("已停止", Colors.Gray);
            }
        }

        private void OnMessageExtracted(object sender, ChatMessage message)
        {
            // 在UI线程中更新
            Dispatcher.Invoke(() =>
            {
                _messageCount++;
                UpdateMessageCount();
                
                // 触发消息接收事件
                MessageReceived?.Invoke(this, message);
            });
        }

        private void OnStatusChanged(object sender, string status)
        {
            Dispatcher.Invoke(() =>
            {
                UpdateStatus(status, Colors.Blue);
            });
        }

        private void UpdateStatus(string status, Color color)
        {
            // 确保在UI线程中执行
            if (Dispatcher.CheckAccess())
            {
                StatusText.Text = status;
                StatusIndicator.Fill = new SolidColorBrush(color);
            }
            else
            {
                Dispatcher.Invoke(() =>
                {
                    StatusText.Text = status;
                    StatusIndicator.Fill = new SolidColorBrush(color);
                });
            }
        }

        private void UpdateMessageCount()
        {
            // 确保在UI线程中执行
            if (Dispatcher.CheckAccess())
            {
                MessageCountText.Text = $"消息: {_messageCount}";
            }
            else
            {
                Dispatcher.Invoke(() =>
                {
                    MessageCountText.Text = $"消息: {_messageCount}";
                });
            }
        }

        public void NavigateToUrl(string url)
        {
            WebBrowser.LoadUrl(url);
        }

        public string GetCurrentUrl()
        {
            return WebBrowser.Address;
        }

        public bool IsExtracting => _isExtracting;
        public string PlatformNameValue => _platformName;
    }
}
