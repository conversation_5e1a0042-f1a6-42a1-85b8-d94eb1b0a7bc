@echo off
echo 开始编译测试...

REM 尝试使用MSBuild编译项目
echo 正在查找MSBuild...

REM 尝试Visual Studio 2022的MSBuild路径
set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
if exist %MSBUILD_PATH% (
    echo 找到MSBuild: %MSBUILD_PATH%
    %MSBUILD_PATH% Chat.sln /p:Configuration=Debug /p:Platform="Any CPU" /v:minimal
    goto :end
)

REM 尝试Visual Studio 2022 Community的MSBuild路径
set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
if exist %MSBUILD_PATH% (
    echo 找到MSBuild: %MSBUILD_PATH%
    %MSBUILD_PATH% Chat.sln /p:Configuration=Debug /p:Platform="Any CPU" /v:minimal
    goto :end
)

REM 尝试Visual Studio 2019的MSBuild路径
set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
if exist %MSBUILD_PATH% (
    echo 找到MSBuild: %MSBUILD_PATH%
    %MSBUILD_PATH% Chat.sln /p:Configuration=Debug /p:Platform="Any CPU" /v:minimal
    goto :end
)

echo 未找到MSBuild，请确保已安装Visual Studio
echo 请手动在Visual Studio中编译项目

:end
echo 编译测试完成
pause
