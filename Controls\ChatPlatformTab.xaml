<UserControl x:Class="Chat.Controls.ChatPlatformTab"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:wpf="clr-namespace:CefSharp.Wpf;assembly=CefSharp.Wpf">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 平台标题栏 -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="10,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <Image Grid.Column="0" x:Name="PlatformIcon" Width="20" Height="20" Margin="0,0,10,0"/>
                <TextBlock Grid.Column="1" x:Name="PlatformName" Text="平台名称" 
                          Foreground="White" VerticalAlignment="Center" FontWeight="Bold"/>
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Ellipse x:Name="StatusIndicator" Width="10" Height="10" 
                            Fill="Gray" Margin="0,0,5,0"/>
                    <TextBlock x:Name="StatusText" Text="未连接" 
                              Foreground="White" FontSize="10" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- 浏览器容器 -->
        <wpf:ChromiumWebBrowser x:Name="WebBrowser" Grid.Row="1"/>
        
        <!-- 控制栏 -->
        <Border Grid.Row="2" Background="#34495E" Padding="10,5">
            <StackPanel Orientation="Horizontal">
                <Button x:Name="RefreshButton" Content="刷新" Margin="0,0,10,0" 
                       Click="RefreshButton_Click"/>
                <Button x:Name="ExtractButton" Content="开始监听" Margin="0,0,10,0" 
                       Click="ExtractButton_Click"/>
                <TextBlock x:Name="MessageCountText" Text="消息: 0" 
                          Foreground="White" VerticalAlignment="Center" Margin="10,0,0,0"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
