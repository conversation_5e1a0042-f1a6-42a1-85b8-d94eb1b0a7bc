using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using ChatLauncher.Models;

namespace ChatLauncher.Services
{
    /// <summary>
    /// 聊天应用启动器服务
    /// </summary>
    public class ChatAppLauncher
    {
        private const string ChatExePath = @"..\bin\x86\Debug\Chat.exe";
        private const string ChatExePathRelease = @"..\bin\x86\Release\Chat.exe";

        /// <summary>
        /// 启动聊天应用
        /// </summary>
        public async Task<bool> LaunchAsync(LaunchConfig config)
        {
            try
            {
                // 验证配置
                if (!config.IsValid(out string errorMessage))
                {
                    MessageBox.Show($"配置验证失败: {errorMessage}", "错误", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }

                // 查找可执行文件
                string exePath = FindChatExecutable();
                if (string.IsNullOrEmpty(exePath))
                {
                    MessageBox.Show("找不到Chat.exe文件！\n\n请确保：\n1. 已编译Chat项目\n2. 可执行文件在正确的位置", 
                        "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }

                // 生成命令行参数
                string arguments = config.GenerateCommandLineArgs();

                // 创建进程启动信息
                var startInfo = new ProcessStartInfo
                {
                    FileName = exePath,
                    Arguments = arguments,
                    UseShellExecute = true,
                    WorkingDirectory = Path.GetDirectoryName(exePath)
                };

                // 启动进程
                var process = Process.Start(startInfo);
                
                if (process != null)
                {
                    // 更新配置使用统计
                    config.LastUsedTime = DateTime.Now;
                    config.UseCount++;

                    // 记录启动日志
                    LogLaunch(config, exePath, arguments);

                    return true;
                }
                else
                {
                    MessageBox.Show("启动进程失败！", "错误", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// 查找Chat可执行文件
        /// </summary>
        private string FindChatExecutable()
        {
            // 尝试多个可能的路径
            string[] possiblePaths = {
                ChatExePath,
                ChatExePathRelease,
                @"..\Chat\bin\x86\Debug\Chat.exe",
                @"..\Chat\bin\x86\Release\Chat.exe",
                @"bin\x86\Debug\Chat.exe",
                @"bin\x86\Release\Chat.exe",
                @"Chat.exe"
            };

            foreach (string path in possiblePaths)
            {
                string fullPath = Path.GetFullPath(path);
                if (File.Exists(fullPath))
                {
                    return fullPath;
                }
            }

            return null;
        }

        /// <summary>
        /// 检查Chat应用是否可用
        /// </summary>
        public bool IsChatAppAvailable()
        {
            return !string.IsNullOrEmpty(FindChatExecutable());
        }

        /// <summary>
        /// 获取Chat应用版本信息
        /// </summary>
        public string GetChatAppVersion()
        {
            try
            {
                string exePath = FindChatExecutable();
                if (string.IsNullOrEmpty(exePath))
                    return "未找到";

                var versionInfo = FileVersionInfo.GetVersionInfo(exePath);
                return versionInfo.FileVersion ?? "未知版本";
            }
            catch
            {
                return "获取版本失败";
            }
        }

        /// <summary>
        /// 获取Chat应用路径
        /// </summary>
        public string GetChatAppPath()
        {
            return FindChatExecutable() ?? "未找到";
        }

        /// <summary>
        /// 测试启动（不实际启动，只验证参数）
        /// </summary>
        public bool TestLaunch(LaunchConfig config, out string commandLine)
        {
            commandLine = "";

            try
            {
                if (!config.IsValid(out string errorMessage))
                {
                    commandLine = $"配置验证失败: {errorMessage}";
                    return false;
                }

                string exePath = FindChatExecutable();
                if (string.IsNullOrEmpty(exePath))
                {
                    commandLine = "找不到Chat.exe文件";
                    return false;
                }

                string arguments = config.GenerateCommandLineArgs();
                commandLine = $"\"{exePath}\" {arguments}";
                return true;
            }
            catch (Exception ex)
            {
                commandLine = $"测试失败: {ex.Message}";
                return false;
            }
        }

        /// <summary>
        /// 启动多个实例
        /// </summary>
        public async Task<int> LaunchMultipleAsync(LaunchConfig config, int instanceCount)
        {
            int successCount = 0;

            for (int i = 0; i < instanceCount; i++)
            {
                // 为每个实例创建独立配置
                var instanceConfig = config.Clone();
                instanceConfig.Name = $"{config.Name} - 实例{i + 1}";

                // 如果指定了用户数据目录，为每个实例创建子目录
                if (!string.IsNullOrEmpty(instanceConfig.UserDataDir))
                {
                    instanceConfig.UserDataDir = Path.Combine(instanceConfig.UserDataDir, $"instance_{i + 1}");
                }

                bool success = await LaunchAsync(instanceConfig);
                if (success)
                {
                    successCount++;
                    
                    // 实例间延迟，避免同时启动造成冲突
                    if (i < instanceCount - 1)
                    {
                        await Task.Delay(2000);
                    }
                }
            }

            return successCount;
        }

        /// <summary>
        /// 记录启动日志
        /// </summary>
        private void LogLaunch(LaunchConfig config, string exePath, string arguments)
        {
            try
            {
                string logDir = "logs";
                if (!Directory.Exists(logDir))
                {
                    Directory.CreateDirectory(logDir);
                }

                string logFile = Path.Combine(logDir, $"launcher-{DateTime.Now:yyyy-MM-dd}.log");
                string logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - 启动配置: {config.Name} | 路径: {exePath} | 参数: {arguments}{Environment.NewLine}";
                
                File.AppendAllText(logFile, logEntry);
            }
            catch
            {
                // 忽略日志记录错误
            }
        }

        /// <summary>
        /// 获取运行中的Chat实例
        /// </summary>
        public Process[] GetRunningChatInstances()
        {
            try
            {
                return Process.GetProcessesByName("Chat");
            }
            catch
            {
                return new Process[0];
            }
        }

        /// <summary>
        /// 关闭所有Chat实例
        /// </summary>
        public int CloseAllChatInstances()
        {
            try
            {
                var processes = GetRunningChatInstances();
                int closedCount = 0;

                foreach (var process in processes)
                {
                    try
                    {
                        process.CloseMainWindow();
                        if (!process.WaitForExit(5000))
                        {
                            process.Kill();
                        }
                        closedCount++;
                    }
                    catch
                    {
                        // 忽略单个进程关闭失败
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }

                return closedCount;
            }
            catch
            {
                return 0;
            }
        }
    }
}
