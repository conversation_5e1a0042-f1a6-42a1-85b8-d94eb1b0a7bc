@echo off
chcp 65001 > nul
echo ========================================
echo Compile with Large Button Layout
echo ========================================
echo.

echo Step 1: Clean previous build
if exist "ChatLauncher\bin" rmdir /s /q "ChatLauncher\bin"
if exist "ChatLauncher\obj" rmdir /s /q "ChatLauncher\obj"
echo Build folders cleaned.

echo.
echo Step 2: Large button improvements made:
echo ✓ Base button size: MinWidth=120px, MinHeight=42px
echo ✓ Base button padding: 25,15px (much larger)
echo ✓ Header buttons: 100x40px with 20,12px padding
echo ✓ Config buttons: 90x38px with 18,10px padding
echo ✓ Launch button: 180x50px with 35,18px padding
echo ✓ Status buttons: 110-140px width, 38px height
echo ✓ Window size: 1150x720px (larger to fit buttons)
echo ✓ Improved margins: 10-18px spacing

echo.
echo Step 3: Compile ChatLauncher with large button layout
echo Command: msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal
echo.

msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ ChatLauncher compiled successfully with large buttons!
    
    if exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
        echo ✓ Output file created: ChatLauncher\bin\Debug\ChatLauncher.exe
        
        echo.
        echo ========================================
        echo SUCCESS: Large Button Layout Complete!
        echo ========================================
        echo.
        echo Button size improvements:
        echo.
        echo 📏 Base Button Style:
        echo    • MinWidth: 100px → 120px
        echo    • MinHeight: 36px → 42px
        echo    • Padding: 20,12px → 25,15px
        echo    • Margin: 8px → 10px
        echo.
        echo 🔄 Header Buttons (刷新/设置):
        echo    • Size: 100x40px
        echo    • Padding: 20,12px
        echo    • Spacing: 15px between buttons
        echo.
        echo ⚙️ Config Buttons (新建/编辑/删除):
        echo    • Size: 90x38px
        echo    • Padding: 18,10px
        echo    • Spacing: 12px between buttons
        echo.
        echo 🚀 Launch Button (启动应用):
        echo    • Size: 180x50px (extra large)
        echo    • Padding: 35,18px
        echo    • FontSize: 16px Bold
        echo.
        echo 📋 Status Buttons (日志/关闭):
        echo    • View Logs: 110x38px
        echo    • Close All: 140x38px
        echo    • Padding: 20,10px
        echo    • Spacing: 18px between buttons
        echo.
        echo 🖼️ Window Improvements:
        echo    • Size: 1100x700px → 1150x720px
        echo    • MinSize: 1000x650px
        echo    • Better accommodation for large buttons
        echo.
        
        set /p choice=Do you want to start ChatLauncher with large buttons? (y/n): 
        if /i "%choice%"=="y" (
            echo.
            echo Starting ChatLauncher with large button layout...
            echo.
            echo You should now see:
            echo • Much larger, more visible buttons
            echo • Better text readability
            echo • Improved spacing throughout
            echo • More comfortable clicking experience
            echo • Professional, accessible interface
            echo • No more cut-off button text
            echo.
            start ChatLauncher\bin\Debug\ChatLauncher.exe
            echo.
            echo ✓ ChatLauncher started with large buttons!
            echo.
            echo The interface should now be:
            echo • Much more accessible and user-friendly
            echo • Easier to read and interact with
            echo • Better suited for different screen sizes
            echo • More professional in appearance
            echo • Comfortable for extended use
        ) else (
            echo.
            echo ChatLauncher with large buttons is ready!
            echo Execute: ChatLauncher\bin\Debug\ChatLauncher.exe
        )
    ) else (
        echo ✗ Output file not found
        echo This indicates a compilation issue.
    )
) else (
    echo.
    echo ✗ Compilation failed!
    echo Please check the error messages above for specific issues.
)

echo.
echo ========================================
echo Large Button Layout Summary
echo ========================================
echo.
echo This version provides:
echo.
echo ✅ Accessibility Improvements:
echo    • Larger click targets for easier interaction
echo    • Better text visibility and readability
echo    • Improved spacing for visual clarity
echo    • More comfortable user experience
echo.
echo ✅ Visual Enhancements:
echo    • Professional, modern appearance
echo    • Consistent button sizing throughout
echo    • Better visual hierarchy
echo    • Improved layout proportions
echo.
echo ✅ Usability Features:
echo    • No more cut-off button text
echo    • Easier navigation and interaction
echo    • Better support for different screen sizes
echo    • More intuitive interface design
echo.
echo ✅ Technical Improvements:
echo    • Responsive layout design
echo    • Proper minimum window sizing
echo    • Optimized spacing and margins
echo    • Enhanced visual feedback
echo.
echo The ChatLauncher interface is now much more user-friendly!
pause
