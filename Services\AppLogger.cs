using System;

namespace Chat.Services
{
    /// <summary>
    /// 应用程序统一日志接口
    /// 自动选择可用的日志实现（NLog或SimpleLogger）
    /// </summary>
    public static class AppLogger
    {
        private static bool _useNLog = false;
        private static bool _isInitialized = false;

        /// <summary>
        /// 初始化日志系统
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized) return;

            try
            {
                // 尝试使用NLog
                var nlogType = Type.GetType("NLog.LogManager, NLog");
                if (nlogType != null)
                {
                    LoggingService.Initialize();
                    _useNLog = true;
                    Console.WriteLine("使用NLog日志系统");
                }
                else
                {
                    // 回退到简单日志实现
                    SimpleLogger.Initialize();
                    _useNLog = false;
                    Console.WriteLine("使用SimpleLogger日志系统");
                }

                _isInitialized = true;
                LogInfo("应用程序日志系统初始化完成");
            }
            catch (Exception ex)
            {
                // 如果都失败了，至少输出到控制台
                Console.WriteLine($"日志系统初始化失败: {ex.Message}");
                _useNLog = false;
                SimpleLogger.Initialize();
                _isInitialized = true;
            }
        }

        #region 通用日志方法

        /// <summary>
        /// 记录信息日志
        /// </summary>
        public static void LogInfo(string message, params object[] args)
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogInfo(message, args);
            else
                SimpleLogger.LogInfo(message, args);
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        public static void LogDebug(string message, params object[] args)
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogDebug(message, args);
            else
                SimpleLogger.LogDebug(message, args);
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        public static void LogWarning(string message, params object[] args)
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogWarning(message, args);
            else
                SimpleLogger.LogWarning(message, args);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        public static void LogError(string message, Exception exception = null, params object[] args)
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogError(message, exception, args);
            else
                SimpleLogger.LogError(message, exception, args);
        }

        /// <summary>
        /// 记录致命错误日志
        /// </summary>
        public static void LogFatal(string message, Exception exception = null, params object[] args)
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogFatal(message, exception, args);
            else
                SimpleLogger.LogFatal(message, exception, args);
        }

        #endregion

        #region HTTP请求日志

        /// <summary>
        /// 记录HTTP请求开始
        /// </summary>
        public static void LogHttpRequestStart(string method, string url, string headers = null)
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogHttpRequestStart(method, url, headers);
            else
                SimpleLogger.LogHttpRequestStart(method, url, headers);
        }

        /// <summary>
        /// 记录HTTP请求完成
        /// </summary>
        public static void LogHttpRequestComplete(string method, string url, int statusCode, long elapsedMs, string responseSize = null)
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogHttpRequestComplete(method, url, statusCode, elapsedMs, responseSize);
            else
                SimpleLogger.LogHttpRequestComplete(method, url, statusCode, elapsedMs, responseSize);
        }

        /// <summary>
        /// 记录HTTP请求错误
        /// </summary>
        public static void LogHttpRequestError(string method, string url, Exception exception, long elapsedMs = 0)
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogHttpRequestError(method, url, exception, elapsedMs);
            else
                SimpleLogger.LogHttpRequestError(method, url, exception, elapsedMs);
        }

        #endregion

        #region TCP连接日志

        /// <summary>
        /// 记录TCP连接建立
        /// </summary>
        public static void LogTcpConnectionStart(string host, int port, string connectionType = "TCP")
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogTcpConnectionStart(host, port, connectionType);
            else
                SimpleLogger.LogTcpConnectionStart(host, port, connectionType);
        }

        /// <summary>
        /// 记录TCP连接成功
        /// </summary>
        public static void LogTcpConnectionSuccess(string host, int port, long elapsedMs, string connectionType = "TCP")
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogTcpConnectionSuccess(host, port, elapsedMs, connectionType);
            else
                SimpleLogger.LogTcpConnectionSuccess(host, port, elapsedMs, connectionType);
        }

        /// <summary>
        /// 记录TCP连接失败
        /// </summary>
        public static void LogTcpConnectionError(string host, int port, Exception exception, string connectionType = "TCP")
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogTcpConnectionError(host, port, exception, connectionType);
            else
                SimpleLogger.LogTcpConnectionError(host, port, exception, connectionType);
        }

        #endregion

        #region 文件操作日志

        /// <summary>
        /// 记录文件读取操作
        /// </summary>
        public static void LogFileRead(string filePath, long fileSize = 0, long elapsedMs = 0)
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogFileRead(filePath, fileSize, elapsedMs);
            else
                SimpleLogger.LogFileRead(filePath, fileSize, elapsedMs);
        }

        /// <summary>
        /// 记录文件写入操作
        /// </summary>
        public static void LogFileWrite(string filePath, long fileSize = 0, long elapsedMs = 0)
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogFileWrite(filePath, fileSize, elapsedMs);
            else
                SimpleLogger.LogFileWrite(filePath, fileSize, elapsedMs);
        }

        /// <summary>
        /// 记录文件操作错误
        /// </summary>
        public static void LogFileError(string operation, string filePath, Exception exception)
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogFileError(operation, filePath, exception);
            else
                SimpleLogger.LogFileError(operation, filePath, exception);
        }

        #endregion

        #region 数据库操作日志

        /// <summary>
        /// 记录数据库查询
        /// </summary>
        public static void LogDatabaseQuery(string query, long elapsedMs = 0, int recordCount = 0)
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogDatabaseQuery(query, elapsedMs, recordCount);
            else
                SimpleLogger.LogDatabaseQuery(query, elapsedMs, recordCount);
        }

        /// <summary>
        /// 记录数据库错误
        /// </summary>
        public static void LogDatabaseError(string operation, Exception exception, string additionalInfo = null)
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogDatabaseError(operation, exception, additionalInfo);
            else
                SimpleLogger.LogDatabaseError(operation, exception, additionalInfo);
        }

        #endregion

        #region 代理操作日志

        /// <summary>
        /// 记录代理配置
        /// </summary>
        public static void LogProxyConfig(string proxyType, string host, int port, bool hasAuth = false)
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogProxyConfig(proxyType, host, port, hasAuth);
            else
                SimpleLogger.LogProxyConfig(proxyType, host, port, hasAuth);
        }

        /// <summary>
        /// 记录代理连接测试
        /// </summary>
        public static void LogProxyTest(string proxyUrl, bool success, long elapsedMs = 0, string errorMessage = null)
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogProxyTest(proxyUrl, success, elapsedMs, errorMessage);
            else
                SimpleLogger.LogProxyTest(proxyUrl, success, elapsedMs, errorMessage);
        }

        #endregion

        #region 聊天提取日志

        /// <summary>
        /// 记录聊天提取开始
        /// </summary>
        public static void LogChatExtractionStart(string platform, string url)
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogChatExtractionStart(platform, url);
            else
                SimpleLogger.LogChatExtractionStart(platform, url);
        }

        /// <summary>
        /// 记录聊天消息提取
        /// </summary>
        public static void LogChatMessagesExtracted(string platform, int messageCount, long elapsedMs = 0)
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogChatMessagesExtracted(platform, messageCount, elapsedMs);
            else
                SimpleLogger.LogChatMessagesExtracted(platform, messageCount, elapsedMs);
        }

        /// <summary>
        /// 记录聊天提取错误
        /// </summary>
        public static void LogChatExtractionError(string platform, Exception exception, string additionalInfo = null)
        {
            if (!_isInitialized) Initialize();

            if (_useNLog)
                LoggingService.LogChatExtractionError(platform, exception, additionalInfo);
            else
                SimpleLogger.LogChatExtractionError(platform, exception, additionalInfo);
        }

        #endregion

        /// <summary>
        /// 关闭日志服务
        /// </summary>
        public static void Shutdown()
        {
            if (!_isInitialized) return;

            try
            {
                LogInfo("应用程序日志系统正在关闭...");
                
                if (_useNLog)
                {
                    LoggingService.Shutdown();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"日志系统关闭失败: {ex.Message}");
            }
        }
    }
}
