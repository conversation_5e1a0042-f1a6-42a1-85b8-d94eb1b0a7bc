using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using ChatLauncher.Models;
using ChatLauncher.Services;

namespace ChatLauncher.ViewModels
{
    /// <summary>
    /// Main window view model
    /// </summary>
    public class MainViewModel : INotifyPropertyChanged
    {
        private readonly ChatAppLauncher _launcher;
        private LaunchConfig _selectedConfig;
        private string _statusText;
        private bool _isLaunching;

        public MainViewModel()
        {
            _launcher = new ChatAppLauncher();
            Configs = new ObservableCollection<LaunchConfig>();
            StatusText = "Ready";
            
            LoadConfigs();
        }

        #region Properties

        /// <summary>
        /// Available launch configurations
        /// </summary>
        public ObservableCollection<LaunchConfig> Configs { get; }

        /// <summary>
        /// Currently selected configuration
        /// </summary>
        public LaunchConfig SelectedConfig
        {
            get => _selectedConfig;
            set
            {
                if (SetProperty(ref _selectedConfig, value))
                {
                    OnPropertyChanged(nameof(CanLaunch));
                }
            }
        }

        /// <summary>
        /// Status text
        /// </summary>
        public string StatusText
        {
            get => _statusText;
            set => SetProperty(ref _statusText, value);
        }

        /// <summary>
        /// Whether currently launching
        /// </summary>
        public bool IsLaunching
        {
            get => _isLaunching;
            set
            {
                if (SetProperty(ref _isLaunching, value))
                {
                    OnPropertyChanged(nameof(CanLaunch));
                }
            }
        }

        /// <summary>
        /// Whether can launch
        /// </summary>
        public bool CanLaunch => !IsLaunching && SelectedConfig != null && _launcher.IsChatAppAvailable();

        #endregion

        #region Commands

        private ICommand _launchCommand;
        public ICommand LaunchCommand => _launchCommand ??= new RelayCommand(async () =>
        {
            if (SelectedConfig == null) return;

            try
            {
                IsLaunching = true;
                StatusText = "Launching...";

                bool success = await _launcher.LaunchAsync(SelectedConfig);
                StatusText = success ? "Launch successful" : "Launch failed";
            }
            catch (Exception ex)
            {
                StatusText = $"Launch failed: {ex.Message}";
            }
            finally
            {
                IsLaunching = false;
            }
        });

        private ICommand _refreshCommand;
        public ICommand RefreshCommand => _refreshCommand ??= new RelayCommand(() =>
        {
            LoadConfigs();
            StatusText = "Refreshed";
        });

        #endregion

        #region Methods

        private void LoadConfigs()
        {
            try
            {
                Configs.Clear();
                var defaultConfigs = PredefinedConfigs.GetDefaultConfigs();
                
                foreach (var config in defaultConfigs)
                {
                    Configs.Add(config);
                }

                if (Configs.Count > 0)
                {
                    SelectedConfig = Configs[0];
                }

                StatusText = $"Loaded {Configs.Count} configurations";
            }
            catch (Exception ex)
            {
                StatusText = $"Failed to load configurations: {ex.Message}";
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }

    /// <summary>
    /// Simple relay command implementation
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool> _canExecute;

        public RelayCommand(Action execute, Func<bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler CanExecuteChanged
        {
            add => CommandManager.RequerySuggested += value;
            remove => CommandManager.RequerySuggested -= value;
        }

        public bool CanExecute(object parameter) => _canExecute?.Invoke() ?? true;

        public void Execute(object parameter) => _execute();
    }
}
