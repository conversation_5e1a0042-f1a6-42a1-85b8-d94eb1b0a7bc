using CefSharp;
using CefSharp.Wpf;
using Chat.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Chat.Services
{
    public class WebChatExtractor
    {
        private readonly ChromiumWebBrowser _browser;
        private readonly string _platformName;
        
        public event EventHandler<ChatMessage> MessageExtracted;
        public event EventHandler<string> StatusChanged;
        
        public WebChatExtractor(ChromiumWebBrowser browser, string platformName)
        {
            _browser = browser;
            _platformName = platformName;
            
            // 注册JavaScript回调
            _browser.JavaScriptObjectRepository.Register("chatExtractor", this, false);
        }
        
        public async Task<bool> InjectExtractionScriptAsync()
        {
            try
            {
                var script = GetExtractionScript(_platformName);
                var result = await _browser.EvaluateScriptAsync(script);
                return result.Success;
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"脚本注入失败: {ex.Message}");
                return false;
            }
        }
        
        private string GetExtractionScript(string platform)
        {
            switch (platform.ToLower())
            {
                case "whatsapp":
                    return GetWhatsAppScript();
                case "telegram":
                    return GetTelegramScript();
                case "wechat":
                    return GetWeChatScript();
                default:
                    return GetGenericScript();
            }
        }
        
        private string GetWhatsAppScript()
        {
            return @"
                (function() {
                    let lastMessageCount = 0;
                    
                    function extractMessages() {
                        const messages = [];
                        const messageElements = document.querySelectorAll('[data-testid=""msg-container""]');
                        
                        messageElements.forEach((element, index) => {
                            if (index >= lastMessageCount) {
                                const messageData = {
                                    id: index,
                                    content: element.querySelector('.selectable-text')?.innerText || '',
                                    sender: element.querySelector('[data-testid=""message-author""]')?.innerText || 'Unknown',
                                    timestamp: new Date().toISOString(),
                                    platform: 'WhatsApp'
                                };
                                messages.push(messageData);
                            }
                        });
                        
                        lastMessageCount = messageElements.length;
                        
                        if (messages.length > 0) {
                            window.chatExtractor.onMessagesExtracted(JSON.stringify(messages));
                        }
                    }
                    
                    // 监听DOM变化
                    const observer = new MutationObserver(extractMessages);
                    const chatContainer = document.querySelector('[data-testid=""conversation-panel-messages""]');
                    
                    if (chatContainer) {
                        observer.observe(chatContainer, { childList: true, subtree: true });
                        extractMessages(); // 初始提取
                    }
                    
                    return 'WhatsApp监听器已启动';
                })();
            ";
        }
        
        private string GetTelegramScript()
        {
            return @"
                (function() {
                    let lastMessageCount = 0;
                    
                    function extractMessages() {
                        const messages = [];
                        const messageElements = document.querySelectorAll('.message');
                        
                        messageElements.forEach((element, index) => {
                            if (index >= lastMessageCount) {
                                const messageData = {
                                    id: index,
                                    content: element.querySelector('.message-content')?.innerText || '',
                                    sender: element.querySelector('.peer-title')?.innerText || 'Unknown',
                                    timestamp: new Date().toISOString(),
                                    platform: 'Telegram'
                                };
                                messages.push(messageData);
                            }
                        });
                        
                        lastMessageCount = messageElements.length;
                        
                        if (messages.length > 0) {
                            window.chatExtractor.onMessagesExtracted(JSON.stringify(messages));
                        }
                    }
                    
                    const observer = new MutationObserver(extractMessages);
                    const chatContainer = document.querySelector('.messages-container');
                    
                    if (chatContainer) {
                        observer.observe(chatContainer, { childList: true, subtree: true });
                        extractMessages();
                    }
                    
                    return 'Telegram监听器已启动';
                })();
            ";
        }
        
        private string GetWeChatScript()
        {
            return @"
                (function() {
                    // 微信网页版的DOM结构可能会变化，需要适配
                    let lastMessageCount = 0;
                    
                    function extractMessages() {
                        const messages = [];
                        // 微信的消息选择器（可能需要根据实际情况调整）
                        const messageElements = document.querySelectorAll('.msg');
                        
                        messageElements.forEach((element, index) => {
                            if (index >= lastMessageCount) {
                                const messageData = {
                                    id: index,
                                    content: element.querySelector('.msg_content')?.innerText || '',
                                    sender: element.querySelector('.msg_sender')?.innerText || 'Unknown',
                                    timestamp: new Date().toISOString(),
                                    platform: 'WeChat'
                                };
                                messages.push(messageData);
                            }
                        });
                        
                        lastMessageCount = messageElements.length;
                        
                        if (messages.length > 0) {
                            window.chatExtractor.onMessagesExtracted(JSON.stringify(messages));
                        }
                    }
                    
                    const observer = new MutationObserver(extractMessages);
                    const chatContainer = document.querySelector('.chat_bd');
                    
                    if (chatContainer) {
                        observer.observe(chatContainer, { childList: true, subtree: true });
                        extractMessages();
                    }
                    
                    return '微信监听器已启动';
                })();
            ";
        }
        
        private string GetGenericScript()
        {
            return @"
                (function() {
                    return '通用监听器已启动';
                })();
            ";
        }
        
        // 这个方法会被JavaScript调用
        public void OnMessagesExtracted(string messagesJson)
        {
            try
            {
                var messages = JsonSerializer.Deserialize<List<Dictionary<string, object>>>(messagesJson);
                
                foreach (var msgData in messages)
                {
                    var message = new ChatMessage
                    {
                        Id = Convert.ToInt32(msgData["id"]),
                        Content = msgData["content"].ToString(),
                        Sender = msgData["sender"].ToString(),
                        Timestamp = DateTime.Parse(msgData["timestamp"].ToString()),
                        Type = MessageType.Text
                    };
                    
                    MessageExtracted?.Invoke(this, message);
                }
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"消息解析失败: {ex.Message}");
            }
        }
    }
}
