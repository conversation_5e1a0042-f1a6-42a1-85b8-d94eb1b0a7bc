using CefSharp;
using CefSharp.Wpf;
using Chat.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Threading;

namespace Chat.Services
{
    public class WebChatExtractor
    {
        private readonly ChromiumWebBrowser _browser;
        private readonly string _platformName;
        private DispatcherTimer _extractionTimer;
        private bool _isExtracting = false;

        public event EventHandler<ChatMessage> MessageExtracted;
        public event EventHandler<string> StatusChanged;
        
        public WebChatExtractor(ChromiumWebBrowser browser, string platformName)
        {
            _browser = browser;
            _platformName = platformName;

            // 初始化定时器
            _extractionTimer = new DispatcherTimer();
            _extractionTimer.Interval = TimeSpan.FromSeconds(2); // 每2秒检查一次
            _extractionTimer.Tick += OnExtractionTimer_Tick;
        }

        // 线程安全的UI更新辅助方法
        private void SafeInvokeStatusChanged(string status)
        {
            System.Windows.Application.Current?.Dispatcher.Invoke(() =>
            {
                StatusChanged?.Invoke(this, status);
            });
        }
        
        public async Task<bool> InjectExtractionScriptAsync()
        {
            try
            {
                if (!_isExtracting)
                {
                    // 首先测试页面是否可访问
                    var testScript = "document.title + ' | ' + window.location.href";
                    var testResult = await _browser.EvaluateScriptAsync(testScript);

                    if (testResult.Success)
                    {
                        SafeInvokeStatusChanged($"页面信息: {testResult.Result}");

                        _isExtracting = true;
                        _extractionTimer.Start();
                        SafeInvokeStatusChanged("开始监听消息");
                        return true;
                    }
                    else
                    {
                        SafeInvokeStatusChanged($"页面访问测试失败: {testResult.Message}");
                        return false;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                SafeInvokeStatusChanged($"启动失败: {ex.Message}");
                return false;
            }
        }

        public void StopExtraction()
        {
            if (_isExtracting)
            {
                _isExtracting = false;
                _extractionTimer.Stop();

                SafeInvokeStatusChanged("停止监听消息");
            }
        }

        private async void OnExtractionTimer_Tick(object sender, EventArgs e)
        {
            if (!_isExtracting) return;

            try
            {
                var script = GetExtractionScript(_platformName);
                SafeInvokeStatusChanged($"正在执行脚本...");

                var result = await _browser.EvaluateScriptAsync(script);

                if (result.Success && result.Result != null)
                {
                    var resultString = result.Result.ToString();
                    SafeInvokeStatusChanged($"脚本执行成功，结果长度: {resultString?.Length ?? 0}");

                    if (!string.IsNullOrEmpty(resultString) && resultString != "null" && resultString != "[]")
                    {
                        ProcessExtractedData(resultString);
                    }
                    else
                    {
                        SafeInvokeStatusChanged("未找到消息或页面未加载完成");
                    }
                }
                else
                {
                    SafeInvokeStatusChanged($"脚本执行失败: {result.Message}");
                }
            }
            catch (Exception ex)
            {
                SafeInvokeStatusChanged($"提取错误: {ex.Message}");
            }
        }
        
        private string GetExtractionScript(string platform)
        {
            switch (platform.ToLower())
            {
                case "whatsapp":
                    return GetWhatsAppScript();
                case "telegram":
                    return GetTelegramScript();
                case "wechat":
                    return GetWeChatScript();
                default:
                    return GetGenericScript();
            }
        }
        
        private string GetWhatsAppScript()
        {
            return @"
                (function() {
                    const messages = [];
                    const messageElements = document.querySelectorAll('[data-testid=""msg-container""]');

                    messageElements.forEach((element, index) => {
                        const contentElement = element.querySelector('.selectable-text');
                        const authorElement = element.querySelector('[data-testid=""message-author""]');

                        if (contentElement) {
                            const messageData = {
                                id: index,
                                content: contentElement.innerText || '',
                                sender: authorElement?.innerText || 'Unknown',
                                timestamp: new Date().toISOString(),
                                platform: 'WhatsApp'
                            };
                            messages.push(messageData);
                        }
                    });

                    return JSON.stringify(messages);
                })();
            ";
        }
        
        private string GetTelegramScript()
        {
            return @"
                (function() {
                    const messages = [];
                    const messageElements = document.querySelectorAll('.message');

                    messageElements.forEach((element, index) => {
                        const contentElement = element.querySelector('.message-content');
                        const senderElement = element.querySelector('.peer-title');

                        if (contentElement) {
                            const messageData = {
                                id: index,
                                content: contentElement.innerText || '',
                                sender: senderElement?.innerText || 'Unknown',
                                timestamp: new Date().toISOString(),
                                platform: 'Telegram'
                            };
                            messages.push(messageData);
                        }
                    });

                    return JSON.stringify(messages);
                })();
            ";
        }
        
        private string GetWeChatScript()
        {
            return @"
                (function() {
                    const messages = [];
                    const messageElements = document.querySelectorAll('.msg');

                    messageElements.forEach((element, index) => {
                        const contentElement = element.querySelector('.msg_content');
                        const senderElement = element.querySelector('.msg_sender');

                        if (contentElement) {
                            const messageData = {
                                id: index,
                                content: contentElement.innerText || '',
                                sender: senderElement?.innerText || 'Unknown',
                                timestamp: new Date().toISOString(),
                                platform: 'WeChat'
                            };
                            messages.push(messageData);
                        }
                    });

                    return JSON.stringify(messages);
                })();
            ";
        }

        private string GetGenericScript()
        {
            return @"
                (function() {
                    try {
                        const messages = [];

                        // 尝试多种常见的消息选择器
                        const selectors = [
                            '.message',
                            '[data-testid=""msg-container""]',
                            '.msg',
                            '.chat-message',
                            '.message-content',
                            'div[role=""listitem""]',
                            '.conversation-message'
                        ];

                        let messageElements = [];
                        for (const selector of selectors) {
                            messageElements = document.querySelectorAll(selector);
                            if (messageElements.length > 0) {
                                console.log('找到消息元素，使用选择器:', selector, '数量:', messageElements.length);
                                break;
                            }
                        }

                        if (messageElements.length === 0) {
                            console.log('未找到任何消息元素');
                            return JSON.stringify({
                                debug: true,
                                message: '未找到消息元素',
                                url: window.location.href,
                                title: document.title
                            });
                        }

                        messageElements.forEach((element, index) => {
                            const textContent = element.innerText || element.textContent || '';
                            if (textContent.trim()) {
                                const messageData = {
                                    id: index,
                                    content: textContent.trim(),
                                    sender: 'Unknown',
                                    timestamp: new Date().toISOString(),
                                    platform: 'Generic'
                                };
                                messages.push(messageData);
                            }
                        });

                        console.log('提取到消息数量:', messages.length);
                        return JSON.stringify(messages);

                    } catch (error) {
                        console.error('脚本执行错误:', error);
                        return JSON.stringify({
                            debug: true,
                            error: error.message,
                            url: window.location.href
                        });
                    }
                })();
            ";
        }
        
        // 处理提取的数据
        private void ProcessExtractedData(string messagesJson)
        {
            try
            {
                SafeInvokeStatusChanged($"开始解析JSON数据...");

                // 首先尝试解析为调试信息
                var debugInfo = JsonConvert.DeserializeObject<Dictionary<string, object>>(messagesJson);
                if (debugInfo != null && debugInfo.ContainsKey("debug"))
                {
                    var debugMessage = debugInfo.ContainsKey("message") ? debugInfo["message"].ToString() : "调试信息";
                    var url = debugInfo.ContainsKey("url") ? debugInfo["url"].ToString() : "未知";
                    SafeInvokeStatusChanged($"调试: {debugMessage} (URL: {url})");
                    return;
                }

                // 尝试解析为消息数组
                var messages = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(messagesJson);

                if (messages != null && messages.Count > 0)
                {
                    SafeInvokeStatusChanged($"找到 {messages.Count} 条消息");

                    // 在UI线程中触发事件
                    System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                    {
                        foreach (var msgData in messages)
                        {
                            try
                            {
                                var message = new ChatMessage
                                {
                                    Id = Convert.ToInt32(msgData["id"]),
                                    Content = msgData["content"].ToString(),
                                    Sender = msgData["sender"].ToString(),
                                    Timestamp = DateTime.Parse(msgData["timestamp"].ToString()),
                                    Type = MessageType.Text,
                                    Platform = _platformName
                                };

                                MessageExtracted?.Invoke(this, message);
                            }
                            catch (Exception msgEx)
                            {
                                SafeInvokeStatusChanged($"处理单条消息失败: {msgEx.Message}");
                            }
                        }
                    });
                }
                else
                {
                    SafeInvokeStatusChanged("解析结果为空或无效");
                }
            }
            catch (Exception ex)
            {
                SafeInvokeStatusChanged($"消息解析失败: {ex.Message}");
                SafeInvokeStatusChanged($"原始数据: {messagesJson?.Substring(0, Math.Min(100, messagesJson.Length))}...");
            }
        }
    }
}
