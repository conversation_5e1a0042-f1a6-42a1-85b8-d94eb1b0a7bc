using CefSharp;
using CefSharp.Wpf;
using Chat.Models;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Threading;

namespace Chat.Services
{
    public class WebChatExtractor
    {
        private readonly ChromiumWebBrowser _browser;
        private readonly string _platformName;
        private DispatcherTimer _extractionTimer;
        private bool _isExtracting = false;

        public event EventHandler<ChatMessage> MessageExtracted;
        public event EventHandler<string> StatusChanged;
        
        public WebChatExtractor(ChromiumWebBrowser browser, string platformName)
        {
            _browser = browser;
            _platformName = platformName;

            // 初始化定时器
            _extractionTimer = new DispatcherTimer();
            _extractionTimer.Interval = TimeSpan.FromSeconds(2); // 每2秒检查一次
            _extractionTimer.Tick += OnExtractionTimer_Tick;
        }

        // 线程安全的UI更新辅助方法
        private void SafeInvokeStatusChanged(string status)
        {
            System.Windows.Application.Current?.Dispatcher.Invoke(() =>
            {
                StatusChanged?.Invoke(this, status);
            });
        }
        
        public async Task<bool> InjectExtractionScriptAsync()
        {
            try
            {
                if (!_isExtracting)
                {
                    _isExtracting = true;
                    _extractionTimer.Start();

                    SafeInvokeStatusChanged("开始监听消息");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                SafeInvokeStatusChanged($"启动失败: {ex.Message}");
                return false;
            }
        }

        public void StopExtraction()
        {
            if (_isExtracting)
            {
                _isExtracting = false;
                _extractionTimer.Stop();

                SafeInvokeStatusChanged("停止监听消息");
            }
        }

        private async void OnExtractionTimer_Tick(object sender, EventArgs e)
        {
            if (!_isExtracting) return;

            try
            {
                var script = GetExtractionScript(_platformName);
                var result = await _browser.EvaluateScriptAsync(script);

                if (result.Success && result.Result != null)
                {
                    var resultString = result.Result.ToString();
                    if (!string.IsNullOrEmpty(resultString) && resultString != "null")
                    {
                        ProcessExtractedData(resultString);
                    }
                }
            }
            catch (Exception ex)
            {
                SafeInvokeStatusChanged($"提取错误: {ex.Message}");
            }
        }
        
        private string GetExtractionScript(string platform)
        {
            switch (platform.ToLower())
            {
                case "whatsapp":
                    return GetWhatsAppScript();
                case "telegram":
                    return GetTelegramScript();
                case "wechat":
                    return GetWeChatScript();
                default:
                    return GetGenericScript();
            }
        }
        
        private string GetWhatsAppScript()
        {
            return @"
                (function() {
                    const messages = [];
                    const messageElements = document.querySelectorAll('[data-testid=""msg-container""]');

                    messageElements.forEach((element, index) => {
                        const contentElement = element.querySelector('.selectable-text');
                        const authorElement = element.querySelector('[data-testid=""message-author""]');

                        if (contentElement) {
                            const messageData = {
                                id: index,
                                content: contentElement.innerText || '',
                                sender: authorElement?.innerText || 'Unknown',
                                timestamp: new Date().toISOString(),
                                platform: 'WhatsApp'
                            };
                            messages.push(messageData);
                        }
                    });

                    return JSON.stringify(messages);
                })();
            ";
        }
        
        private string GetTelegramScript()
        {
            return @"
                (function() {
                    const messages = [];
                    const messageElements = document.querySelectorAll('.message');

                    messageElements.forEach((element, index) => {
                        const contentElement = element.querySelector('.message-content');
                        const senderElement = element.querySelector('.peer-title');

                        if (contentElement) {
                            const messageData = {
                                id: index,
                                content: contentElement.innerText || '',
                                sender: senderElement?.innerText || 'Unknown',
                                timestamp: new Date().toISOString(),
                                platform: 'Telegram'
                            };
                            messages.push(messageData);
                        }
                    });

                    return JSON.stringify(messages);
                })();
            ";
        }
        
        private string GetWeChatScript()
        {
            return @"
                (function() {
                    const messages = [];
                    const messageElements = document.querySelectorAll('.msg');

                    messageElements.forEach((element, index) => {
                        const contentElement = element.querySelector('.msg_content');
                        const senderElement = element.querySelector('.msg_sender');

                        if (contentElement) {
                            const messageData = {
                                id: index,
                                content: contentElement.innerText || '',
                                sender: senderElement?.innerText || 'Unknown',
                                timestamp: new Date().toISOString(),
                                platform: 'WeChat'
                            };
                            messages.push(messageData);
                        }
                    });

                    return JSON.stringify(messages);
                })();
            ";
        }

        private string GetGenericScript()
        {
            return @"
                (function() {
                    return JSON.stringify([]);
                })();
            ";
        }
        
        // 处理提取的数据
        private void ProcessExtractedData(string messagesJson)
        {
            try
            {
                var messages = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(messagesJson);

                if (messages != null && messages.Count > 0)
                {
                    // 在UI线程中触发事件
                    System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                    {
                        foreach (var msgData in messages)
                        {
                            var message = new ChatMessage
                            {
                                Id = Convert.ToInt32(msgData["id"]),
                                Content = msgData["content"].ToString(),
                                Sender = msgData["sender"].ToString(),
                                Timestamp = DateTime.Parse(msgData["timestamp"].ToString()),
                                Type = MessageType.Text,
                                Platform = _platformName
                            };

                            MessageExtracted?.Invoke(this, message);
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                SafeInvokeStatusChanged($"消息解析失败: {ex.Message}");
            }
        }
    }
}
