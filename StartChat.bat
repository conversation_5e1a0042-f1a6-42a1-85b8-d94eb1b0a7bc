@echo off
echo ========================================
echo 聊天集成应用启动器
echo ========================================
echo.

REM 检查可执行文件是否存在
if not exist "bin\x86\Debug\Chat.exe" (
    echo 错误：找不到可执行文件 bin\x86\Debug\Chat.exe
    echo 请先编译项目！
    echo.
    echo 编译方法：
    echo 1. 在Visual Studio中按F6编译
    echo 2. 或者运行 msbuild Chat.sln
    echo.
    pause
    exit /b 1
)

echo 可执行文件路径: bin\x86\Debug\Chat.exe
echo 当前工作目录: %CD%
echo.

REM 如果没有提供参数，显示菜单
if "%1"=="" goto show_menu

REM 如果提供了参数，直接启动
echo 启动应用程序，参数: %*
start bin\x86\Debug\Chat.exe %*
goto end

:show_menu
echo 请选择启动方式：
echo 1. 默认启动
echo 2. 调试模式启动
echo 3. 启动并访问WhatsApp Web
echo 4. 启动并访问Telegram Web
echo 5. 启动并测试代理（需要代理服务器）
echo 6. 启动并访问浏览器指纹测试
echo 7. 显示帮助信息
echo 8. 退出
echo.

set /p choice=请输入选择 (1-8): 

if "%choice%"=="1" goto default_start
if "%choice%"=="2" goto debug_start
if "%choice%"=="3" goto whatsapp_start
if "%choice%"=="4" goto telegram_start
if "%choice%"=="5" goto proxy_start
if "%choice%"=="6" goto fingerprint_start
if "%choice%"=="7" goto show_help
if "%choice%"=="8" goto exit

echo 无效选择，使用默认启动...
goto default_start

:default_start
echo.
echo 默认启动应用程序...
start bin\x86\Debug\Chat.exe
goto end

:debug_start
echo.
echo 调试模式启动应用程序...
start bin\x86\Debug\Chat.exe --debug
goto end

:whatsapp_start
echo.
echo 启动应用程序并访问WhatsApp Web...
start bin\x86\Debug\Chat.exe --url https://web.whatsapp.com --debug
goto end

:telegram_start
echo.
echo 启动应用程序并访问Telegram Web...
start bin\x86\Debug\Chat.exe --url https://web.telegram.org --debug
goto end

:proxy_start
echo.
echo 请输入代理URL（例如：http://127.0.0.1:8080）
set /p proxy_url=代理URL: 
if "%proxy_url%"=="" (
    echo 未输入代理URL，使用默认启动
    goto default_start
)
echo 启动应用程序并使用代理: %proxy_url%
start bin\x86\Debug\Chat.exe --proxy %proxy_url% --url https://www.browserscan.net/zh --debug
goto end

:fingerprint_start
echo.
echo 启动应用程序并访问浏览器指纹测试网站...
start bin\x86\Debug\Chat.exe --url https://www.browserscan.net/zh --debug
goto end

:show_help
echo.
echo 启动应用程序并显示帮助信息...
bin\x86\Debug\Chat.exe --help
echo.
echo 按任意键返回菜单...
pause > nul
goto show_menu

:exit
echo 退出启动器
goto end

:end
echo.
echo 应用程序已启动！
echo 如果遇到问题，请检查：
echo 1. 项目是否已正确编译
echo 2. 所有依赖文件是否存在
echo 3. 查看日志文件：bin\x86\Debug\logs\
echo.
echo 按任意键退出...
pause > nul
