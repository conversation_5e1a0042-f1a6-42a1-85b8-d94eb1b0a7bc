using System;

namespace Chat.Models
{
    /// <summary>
    /// 代理配置模型
    /// </summary>
    public class ProxyConfig
    {
        /// <summary>
        /// 代理类型
        /// </summary>
        public ProxyType Type { get; set; } = ProxyType.Http;

        /// <summary>
        /// 代理服务器地址
        /// </summary>
        public string Host { get; set; } = "";

        /// <summary>
        /// 代理服务器端口
        /// </summary>
        public int Port { get; set; } = 8080;

        /// <summary>
        /// 用户名（如果需要认证）
        /// </summary>
        public string Username { get; set; } = "";

        /// <summary>
        /// 密码（如果需要认证）
        /// </summary>
        public string Password { get; set; } = "";

        /// <summary>
        /// 是否启用代理
        /// </summary>
        public bool Enabled { get; set; } = false;

        /// <summary>
        /// 代理绕过列表（不使用代理的地址）
        /// </summary>
        public string BypassList { get; set; } = "localhost;127.0.0.1;*.local";

        /// <summary>
        /// 是否绕过本地地址
        /// </summary>
        public bool BypassOnLocal { get; set; } = true;

        /// <summary>
        /// 从命令行参数解析代理配置
        /// 支持格式：
        /// - http://host:port
        /// - https://host:port
        /// - socks5://host:port
        /// - *****************************:port
        /// </summary>
        public static ProxyConfig ParseFromString(string proxyString)
        {
            if (string.IsNullOrWhiteSpace(proxyString))
            {
                return new ProxyConfig { Enabled = false };
            }

            try
            {
                var uri = new Uri(proxyString);
                var config = new ProxyConfig
                {
                    Enabled = true,
                    Host = uri.Host,
                    Port = uri.Port > 0 ? uri.Port : GetDefaultPort(uri.Scheme)
                };

                // 解析代理类型
                switch (uri.Scheme.ToLower())
                {
                    case "http":
                        config.Type = ProxyType.Http;
                        break;
                    case "https":
                        config.Type = ProxyType.Https;
                        break;
                    case "socks5":
                        config.Type = ProxyType.Socks5;
                        break;
                    case "socks4":
                        config.Type = ProxyType.Socks4;
                        break;
                    default:
                        config.Type = ProxyType.Http;
                        break;
                }

                // 解析用户名和密码
                if (!string.IsNullOrEmpty(uri.UserInfo))
                {
                    var userInfo = uri.UserInfo.Split(':');
                    if (userInfo.Length >= 1)
                    {
                        config.Username = Uri.UnescapeDataString(userInfo[0]);
                    }
                    if (userInfo.Length >= 2)
                    {
                        config.Password = Uri.UnescapeDataString(userInfo[1]);
                    }
                }

                return config;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析代理配置失败: {ex.Message}");
                return new ProxyConfig { Enabled = false };
            }
        }

        private static int GetDefaultPort(string scheme)
        {
            switch (scheme.ToLower())
            {
                case "http":
                    return 8080;
                case "https":
                    return 8443;
                case "socks5":
                case "socks4":
                    return 1080;
                default:
                    return 8080;
            }
        }

        /// <summary>
        /// 转换为CefSharp可用的代理字符串
        /// </summary>
        public string ToCefSharpProxyString()
        {
            if (!Enabled || string.IsNullOrEmpty(Host))
            {
                return "";
            }

            var scheme = Type == ProxyType.Socks5 ? "socks5" : 
                        Type == ProxyType.Socks4 ? "socks4" : "http";
            
            return $"{scheme}://{Host}:{Port}";
        }

        /// <summary>
        /// 获取代理显示名称
        /// </summary>
        public string GetDisplayName()
        {
            if (!Enabled)
            {
                return "无代理";
            }

            var auth = !string.IsNullOrEmpty(Username) ? $"{Username}@" : "";
            return $"{Type} - {auth}{Host}:{Port}";
        }

        /// <summary>
        /// 验证代理配置是否有效
        /// </summary>
        public bool IsValid()
        {
            if (!Enabled)
            {
                return true;
            }

            return !string.IsNullOrEmpty(Host) && Port > 0 && Port <= 65535;
        }

        public override string ToString()
        {
            return GetDisplayName();
        }
    }

    /// <summary>
    /// 代理类型枚举
    /// </summary>
    public enum ProxyType
    {
        /// <summary>
        /// HTTP代理
        /// </summary>
        Http,

        /// <summary>
        /// HTTPS代理
        /// </summary>
        Https,

        /// <summary>
        /// SOCKS5代理
        /// </summary>
        Socks5,

        /// <summary>
        /// SOCKS4代理
        /// </summary>
        Socks4
    }
}
