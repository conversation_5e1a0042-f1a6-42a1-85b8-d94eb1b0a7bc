@echo off
chcp 65001 > nul
echo ========================================
echo Compile ChatLauncher with HandyControl
echo ========================================
echo.

echo Step 1: Restore NuGet packages for ChatLauncher
echo Restoring packages...
nuget restore ChatLauncher\packages.config -PackagesDirectory packages

if %ERRORLEVEL% NEQ 0 (
    echo ⚠ NuGet restore failed, trying alternative method...
    echo Copying HandyControl from main project packages...
    
    if exist "packages\HandyControl.3.5.1" (
        echo ✓ HandyControl package found in main packages directory
    ) else (
        echo ✗ HandyControl package not found
        echo Please install HandyControl manually:
        echo   nuget install HandyControl -Version 3.5.1 -OutputDirectory packages
        pause
        exit /b 1
    )
)

echo.
echo Step 2: Clean previous build
if exist "ChatLauncher\bin" rmdir /s /q "ChatLauncher\bin"
if exist "ChatLauncher\obj" rmdir /s /q "ChatLauncher\obj"
echo Build folders cleaned.

echo.
echo Step 3: Compile ChatLauncher with HandyControl
echo Command: msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal
echo.

msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ ChatLauncher with HandyControl compiled successfully!
    
    if exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
        echo ✓ Output file created: ChatLauncher\bin\Debug\ChatLauncher.exe
        
        echo.
        echo Checking dependencies:
        if exist "ChatLauncher\bin\Debug\HandyControl.dll" (
            echo ✓ HandyControl.dll found
        ) else (
            echo ✗ HandyControl.dll missing
        )
        
        echo.
        echo File information:
        dir "ChatLauncher\bin\Debug\ChatLauncher.exe"
        
        echo.
        echo ========================================
        echo SUCCESS: HandyControl Integration Complete!
        echo ========================================
        echo.
        echo ChatLauncher now features:
        echo ✓ Modern HandyControl UI components
        echo ✓ Beautiful card-based layout
        echo ✓ Enhanced buttons and controls
        echo ✓ Professional styling
        echo ✓ Improved user experience
        echo.
        
        set /p choice=Do you want to start the new ChatLauncher? (y/n): 
        if /i "%choice%"=="y" (
            echo.
            echo Starting ChatLauncher with HandyControl UI...
            start ChatLauncher\bin\Debug\ChatLauncher.exe
            echo.
            echo ✓ ChatLauncher started! Enjoy the new modern interface!
        )
    ) else (
        echo ✗ Output file not found
    )
) else (
    echo.
    echo ✗ Compilation failed!
    echo.
    echo Common HandyControl integration issues:
    echo 1. Missing HandyControl package - run: nuget install HandyControl
    echo 2. Wrong target framework - ensure .NET Framework 4.8.1
    echo 3. XAML namespace errors - check xmlns:hc declarations
    echo 4. Control name conflicts - verify control names
    echo.
    echo Please check the error messages above for specific issues.
)

echo.
echo ========================================
echo Compilation Complete
echo ========================================
pause
