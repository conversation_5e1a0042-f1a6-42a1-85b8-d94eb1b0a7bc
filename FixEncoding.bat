@echo off
chcp 65001 > nul
echo ========================================
echo File Encoding Fix Tool
echo ========================================
echo.

echo Checking file encoding issues...
echo.

REM Check if Visual Studio is running
tasklist /FI "IMAGENAME eq devenv.exe" 2>NUL | find /I /N "devenv.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo WARNING: Visual Studio is running!
    echo Please close Visual Studio before running this fix.
    echo.
    pause
    exit /b 1
)

echo Step 1: Backup original files...
if not exist "backup" mkdir backup
if exist "ChatLauncher\Properties\*.cs" (
    copy "ChatLauncher\Properties\*.cs" "backup\" > nul 2>&1
    echo - Properties files backed up
)

echo.
echo Step 2: Fix common encoding issues...

REM Fix BOM issues in source files
echo - Checking for BOM issues...

REM List files that might have encoding issues
echo.
echo Files to check:
dir /b <PERSON><PERSON><PERSON><PERSON>ncher\*.cs 2>nul
dir /b Chat<PERSON>auncher\Properties\*.cs 2>nul
dir /b ChatLauncher\Models\*.cs 2>nul
dir /b ChatLauncher\Services\*.cs 2>nul

echo.
echo Step 3: Recommendations for Visual Studio...
echo.
echo To fix encoding issues in Visual Studio:
echo 1. Open each .cs file in Visual Studio
echo 2. Go to File ^> Advanced Save Options
echo 3. Set Encoding to: Unicode (UTF-8 without signature) - Codepage 65001
echo 4. Save the file
echo.
echo Or use the following PowerShell command:
echo Get-ChildItem -Path "ChatLauncher" -Filter "*.cs" -Recurse ^| ForEach-Object { 
echo     $content = Get-Content $_.FullName -Raw
echo     [System.IO.File]::WriteAllText($_.FullName, $content, [System.Text.Encoding]::UTF8)
echo }
echo.

echo Step 4: Project file encoding check...
if exist "ChatLauncher\ChatLauncher.csproj" (
    echo - ChatLauncher.csproj exists
) else (
    echo - WARNING: ChatLauncher.csproj not found!
)

if exist "Chat.sln" (
    echo - Chat.sln exists
) else (
    echo - WARNING: Chat.sln not found!
)

echo.
echo Step 5: Recommended actions...
echo.
echo 1. Close Visual Studio completely
echo 2. Delete bin and obj folders:
echo    - rmdir /s /q ChatLauncher\bin
echo    - rmdir /s /q ChatLauncher\obj
echo 3. Reopen Visual Studio
echo 4. Rebuild solution
echo.

set /p choice=Do you want to clean bin/obj folders now? (y/n): 
if /i "%choice%"=="y" (
    echo.
    echo Cleaning build folders...
    if exist "ChatLauncher\bin" (
        rmdir /s /q "ChatLauncher\bin"
        echo - ChatLauncher\bin deleted
    )
    if exist "ChatLauncher\obj" (
        rmdir /s /q "ChatLauncher\obj"
        echo - ChatLauncher\obj deleted
    )
    if exist "bin" (
        rmdir /s /q "bin"
        echo - bin deleted
    )
    if exist "obj" (
        rmdir /s /q "obj"
        echo - obj deleted
    )
    echo Build folders cleaned!
)

echo.
echo ========================================
echo Encoding Fix Complete
echo ========================================
echo.
echo Next steps:
echo 1. Open Visual Studio
echo 2. Open Chat.sln
echo 3. Build ^> Rebuild Solution
echo 4. Check for any remaining encoding errors
echo.
echo If you still see encoding issues:
echo - Right-click problematic files in Solution Explorer
echo - Select "Open With..." ^> "Source Code (Text) Editor with Encoding"
echo - Choose "Unicode (UTF-8 without signature)"
echo.
pause
