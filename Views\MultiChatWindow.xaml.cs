using Chat.Controls;
using Chat.Models;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace Chat.Views
{
    public partial class MultiChatWindow : Window
    {
        private ObservableCollection<ChatMessage> _unifiedMessages;
        private Dictionary<string, int> _platformMessageCounts;
        private List<ChatPlatformTab> _platformTabs;

        public MultiChatWindow()
        {
            InitializeComponent();
            InitializeData();
            LoadDefaultPlatforms();
        }

        private void InitializeData()
        {
            _unifiedMessages = new ObservableCollection<ChatMessage>();
            _platformMessageCounts = new Dictionary<string, int>();
            _platformTabs = new List<ChatPlatformTab>();
            
            UnifiedMessagesList.ItemsSource = _unifiedMessages;
        }

        private void LoadDefaultPlatforms()
        {
            // 添加默认的聊天平台
            var platforms = new[]
            {
                new { Name = "WhatsApp", Url = "https://web.whatsapp.com/" },
                new { Name = "Telegram", Url = "https://web.telegram.org/" },
                new { Name = "微信", Url = "https://wx.qq.com/" },
                new { Name = "Facebook", Url = "https://www.messenger.com/" },
                new { Name = "Discord", Url = "https://discord.com/app" }
            };

            foreach (var platform in platforms)
            {
                AddPlatform(platform.Name, platform.Url);
            }
        }

        private void AddPlatform(string name, string url)
        {
            // 创建新的平台标签页
            var platformTab = new ChatPlatformTab();
            platformTab.MessageReceived += OnPlatformMessageReceived;
            
            // 创建标签页
            var tabItem = new TabItem
            {
                Header = name,
                Content = platformTab
            };
            
            PlatformTabs.Items.Add(tabItem);
            _platformTabs.Add(platformTab);
            
            // 初始化平台
            platformTab.Initialize(name, url);
            
            // 初始化统计
            _platformMessageCounts[name] = 0;
            UpdatePlatformStats();
        }

        private void OnPlatformMessageReceived(object sender, ChatMessage message)
        {
            // 添加平台信息
            var platformTab = sender as ChatPlatformTab;
            if (platformTab != null)
            {
                message.Platform = platformTab.PlatformNameValue;
            }

            // 添加到统一消息列表
            _unifiedMessages.Add(message);
            
            // 更新统计
            if (_platformMessageCounts.ContainsKey(message.Platform))
            {
                _platformMessageCounts[message.Platform]++;
            }
            
            UpdateTotalMessages();
            UpdatePlatformStats();
            
            // 自动滚动到最新消息
            if (UnifiedMessagesList.Items.Count > 0)
            {
                UnifiedMessagesList.ScrollIntoView(UnifiedMessagesList.Items[UnifiedMessagesList.Items.Count - 1]);
            }
        }

        private void UpdateTotalMessages()
        {
            var total = _platformMessageCounts.Values.Sum();
            TotalMessagesText.Text = $"总消息: {total}";
        }

        private void UpdatePlatformStats()
        {
            PlatformStats.Children.Clear();
            
            foreach (var kvp in _platformMessageCounts)
            {
                var statPanel = new StackPanel { Orientation = System.Windows.Controls.Orientation.Horizontal, Margin = new Thickness(0, 2, 0, 2) };
                
                var nameText = new TextBlock 
                { 
                    Text = kvp.Key + ":", 
                    Foreground = System.Windows.Media.Brushes.White,
                    Width = 80
                };
                
                var countText = new TextBlock 
                { 
                    Text = kvp.Value.ToString(), 
                    Foreground = System.Windows.Media.Brushes.LightGreen,
                    FontWeight = FontWeights.Bold
                };
                
                statPanel.Children.Add(nameText);
                statPanel.Children.Add(countText);
                PlatformStats.Children.Add(statPanel);
            }
        }

        private void AddPlatformButton_Click(object sender, RoutedEventArgs e)
        {
            // 显示添加平台对话框
            var dialog = new AddPlatformDialog();
            if (dialog.ShowDialog() == true)
            {
                AddPlatform(dialog.PlatformName, dialog.PlatformUrl);
            }
        }

        private void StartAllButton_Click(object sender, RoutedEventArgs e)
        {
            foreach (var tab in _platformTabs)
            {
                if (!tab.IsExtracting)
                {
                    // 模拟点击开始监听按钮
                    // 这里需要访问tab的内部控件，可能需要添加公共方法
                }
            }
        }

        private void StopAllButton_Click(object sender, RoutedEventArgs e)
        {
            foreach (var tab in _platformTabs)
            {
                if (tab.IsExtracting)
                {
                    // 模拟点击停止监听按钮
                }
            }
        }
    }

    // 简单的添加平台对话框
    public class AddPlatformDialog : Window
    {
        public string PlatformName { get; private set; }
        public string PlatformUrl { get; private set; }

        public AddPlatformDialog()
        {
            Title = "添加聊天平台";
            Width = 400;
            Height = 200;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;

            var grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            var nameLabel = new Label { Content = "平台名称:" };
            var nameTextBox = new TextBox { Margin = new Thickness(5) };
            var urlLabel = new Label { Content = "网址:" };
            var urlTextBox = new TextBox { Margin = new Thickness(5) };

            var buttonPanel = new StackPanel { Orientation = System.Windows.Controls.Orientation.Horizontal, HorizontalAlignment = HorizontalAlignment.Right, Margin = new Thickness(5) };
            var okButton = new Button { Content = "确定", Width = 75, Margin = new Thickness(5, 0, 0, 0) };
            var cancelButton = new Button { Content = "取消", Width = 75, Margin = new Thickness(5, 0, 0, 0) };

            okButton.Click += (s, e) =>
            {
                PlatformName = nameTextBox.Text;
                PlatformUrl = urlTextBox.Text;
                DialogResult = true;
            };

            cancelButton.Click += (s, e) => DialogResult = false;

            Grid.SetRow(nameLabel, 0); Grid.SetColumn(nameLabel, 0);
            Grid.SetRow(nameTextBox, 0); Grid.SetColumn(nameTextBox, 1);
            Grid.SetRow(urlLabel, 1); Grid.SetColumn(urlLabel, 0);
            Grid.SetRow(urlTextBox, 1); Grid.SetColumn(urlTextBox, 1);

            buttonPanel.Children.Add(okButton);
            buttonPanel.Children.Add(cancelButton);

            grid.Children.Add(nameLabel);
            grid.Children.Add(nameTextBox);
            grid.Children.Add(urlLabel);
            grid.Children.Add(urlTextBox);
            grid.Children.Add(buttonPanel);

            Grid.SetRow(buttonPanel, 2);
            Grid.SetColumnSpan(buttonPanel, 2);

            Content = grid;
        }
    }
}
