using System;
using System.IO;
using System.Threading;

namespace Chat.Services
{
    /// <summary>
    /// 简化的日志实现（如果NLog不可用时使用）
    /// </summary>
    public static class SimpleLogger
    {
        private static readonly object LockObject = new object();
        private static string _logDirectory;
        private static bool _isInitialized = false;

        /// <summary>
        /// 日志级别
        /// </summary>
        public enum LogLevel
        {
            Trace,
            Debug,
            Info,
            Warn,
            Error,
            Fatal
        }

        /// <summary>
        /// 初始化简单日志记录器
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized) return;

            try
            {
                _logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
                if (!Directory.Exists(_logDirectory))
                {
                    Directory.CreateDirectory(_logDirectory);
                }

                var archiveDirectory = Path.Combine(_logDirectory, "archives");
                if (!Directory.Exists(archiveDirectory))
                {
                    Directory.CreateDirectory(archiveDirectory);
                }

                _isInitialized = true;
                WriteLog(LogLevel.Info, "General", "简单日志记录器初始化完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"简单日志记录器初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 写入日志
        /// </summary>
        private static void WriteLog(LogLevel level, string category, string message, Exception exception = null)
        {
            if (!_isInitialized) Initialize();

            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                var threadId = Thread.CurrentThread.ManagedThreadId;
                var logLine = $"{timestamp} [{level.ToString().ToUpper().PadRight(5)}] [{threadId:D3}] {category}: {message}";

                if (exception != null)
                {
                    logLine += $" | Exception: {exception}";
                }

                // 输出到控制台
                Console.WriteLine(logLine);

                // 输出到调试器
                System.Diagnostics.Debug.WriteLine(logLine);

                // 写入文件
                lock (LockObject)
                {
                    // 写入总日志文件
                    var allLogFile = Path.Combine(_logDirectory, $"all-{DateTime.Now:yyyy-MM-dd}.log");
                    File.AppendAllText(allLogFile, logLine + Environment.NewLine);

                    // 写入分类日志文件
                    var categoryLogFile = Path.Combine(_logDirectory, $"{category.ToLower()}-{DateTime.Now:yyyy-MM-dd}.log");
                    File.AppendAllText(categoryLogFile, logLine + Environment.NewLine);

                    // 错误级别写入错误日志文件
                    if (level >= LogLevel.Error)
                    {
                        var errorLogFile = Path.Combine(_logDirectory, $"error-{DateTime.Now:yyyy-MM-dd}.log");
                        File.AppendAllText(errorLogFile, logLine + Environment.NewLine);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"写入日志失败: {ex.Message}");
            }
        }

        #region 通用日志方法

        public static void LogInfo(string message, params object[] args)
        {
            WriteLog(LogLevel.Info, "General", string.Format(message, args));
        }

        public static void LogDebug(string message, params object[] args)
        {
            WriteLog(LogLevel.Debug, "General", string.Format(message, args));
        }

        public static void LogWarning(string message, params object[] args)
        {
            WriteLog(LogLevel.Warn, "General", string.Format(message, args));
        }

        public static void LogError(string message, Exception exception = null, params object[] args)
        {
            WriteLog(LogLevel.Error, "General", string.Format(message, args), exception);
        }

        public static void LogFatal(string message, Exception exception = null, params object[] args)
        {
            WriteLog(LogLevel.Fatal, "General", string.Format(message, args), exception);
        }

        #endregion

        #region HTTP请求日志

        public static void LogHttpRequestStart(string method, string url, string headers = null)
        {
            var message = $"HTTP请求开始: {method} {url}";
            if (!string.IsNullOrEmpty(headers)) message += $" | Headers: {headers}";
            WriteLog(LogLevel.Info, "Http", message);
        }

        public static void LogHttpRequestComplete(string method, string url, int statusCode, long elapsedMs, string responseSize = null)
        {
            var message = $"HTTP请求完成: {method} {url} | 状态码: {statusCode} | 耗时: {elapsedMs}ms";
            if (!string.IsNullOrEmpty(responseSize)) message += $" | 响应大小: {responseSize}";
            WriteLog(LogLevel.Info, "Http", message);
        }

        public static void LogHttpRequestError(string method, string url, Exception exception, long elapsedMs = 0)
        {
            var message = $"HTTP请求失败: {method} {url} | 耗时: {elapsedMs}ms";
            WriteLog(LogLevel.Error, "Http", message, exception);
        }

        #endregion

        #region TCP连接日志

        public static void LogTcpConnectionStart(string host, int port, string connectionType = "TCP")
        {
            WriteLog(LogLevel.Info, "Tcp", $"TCP连接开始: {connectionType} {host}:{port}");
        }

        public static void LogTcpConnectionSuccess(string host, int port, long elapsedMs, string connectionType = "TCP")
        {
            WriteLog(LogLevel.Info, "Tcp", $"TCP连接成功: {connectionType} {host}:{port} | 耗时: {elapsedMs}ms");
        }

        public static void LogTcpConnectionError(string host, int port, Exception exception, string connectionType = "TCP")
        {
            WriteLog(LogLevel.Error, "Tcp", $"TCP连接失败: {connectionType} {host}:{port}", exception);
        }

        #endregion

        #region 文件操作日志

        public static void LogFileRead(string filePath, long fileSize = 0, long elapsedMs = 0)
        {
            var message = $"文件读取: {filePath}";
            if (fileSize > 0) message += $" | 大小: {fileSize} bytes";
            if (elapsedMs > 0) message += $" | 耗时: {elapsedMs}ms";
            WriteLog(LogLevel.Info, "FileOps", message);
        }

        public static void LogFileWrite(string filePath, long fileSize = 0, long elapsedMs = 0)
        {
            var message = $"文件写入: {filePath}";
            if (fileSize > 0) message += $" | 大小: {fileSize} bytes";
            if (elapsedMs > 0) message += $" | 耗时: {elapsedMs}ms";
            WriteLog(LogLevel.Info, "FileOps", message);
        }

        public static void LogFileError(string operation, string filePath, Exception exception)
        {
            WriteLog(LogLevel.Error, "FileOps", $"文件操作失败: {operation} {filePath}", exception);
        }

        #endregion

        #region 数据库操作日志

        public static void LogDatabaseQuery(string query, long elapsedMs = 0, int recordCount = 0)
        {
            var message = $"数据库查询: {query}";
            if (elapsedMs > 0) message += $" | 耗时: {elapsedMs}ms";
            if (recordCount > 0) message += $" | 记录数: {recordCount}";
            WriteLog(LogLevel.Info, "Database", message);
        }

        public static void LogDatabaseError(string operation, Exception exception, string additionalInfo = null)
        {
            var message = $"数据库操作失败: {operation}";
            if (!string.IsNullOrEmpty(additionalInfo)) message += $" | 附加信息: {additionalInfo}";
            WriteLog(LogLevel.Error, "Database", message, exception);
        }

        #endregion

        #region 代理操作日志

        public static void LogProxyConfig(string proxyType, string host, int port, bool hasAuth = false)
        {
            var message = $"代理配置: {proxyType} {host}:{port}";
            if (hasAuth) message += " | 包含认证信息";
            WriteLog(LogLevel.Info, "Proxy", message);
        }

        public static void LogProxyTest(string proxyUrl, bool success, long elapsedMs = 0, string errorMessage = null)
        {
            var message = $"代理测试: {proxyUrl} | 结果: {(success ? "成功" : "失败")}";
            if (elapsedMs > 0) message += $" | 耗时: {elapsedMs}ms";
            if (!string.IsNullOrEmpty(errorMessage)) message += $" | 错误: {errorMessage}";
            WriteLog(success ? LogLevel.Info : LogLevel.Error, "Proxy", message);
        }

        #endregion

        #region 聊天提取日志

        public static void LogChatExtractionStart(string platform, string url)
        {
            WriteLog(LogLevel.Info, "Chat", $"聊天提取开始: {platform} | URL: {url}");
        }

        public static void LogChatMessagesExtracted(string platform, int messageCount, long elapsedMs = 0)
        {
            var message = $"聊天消息提取: {platform} | 消息数: {messageCount}";
            if (elapsedMs > 0) message += $" | 耗时: {elapsedMs}ms";
            WriteLog(LogLevel.Info, "Chat", message);
        }

        public static void LogChatExtractionError(string platform, Exception exception, string additionalInfo = null)
        {
            var message = $"聊天提取失败: {platform}";
            if (!string.IsNullOrEmpty(additionalInfo)) message += $" | 附加信息: {additionalInfo}";
            WriteLog(LogLevel.Error, "Chat", message, exception);
        }

        #endregion
    }
}
