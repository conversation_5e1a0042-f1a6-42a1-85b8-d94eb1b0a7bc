# 🌐 代理功能使用指南

## 🎯 功能概述

聊天集成应用现在支持通过命令行参数配置代理服务器，可以有效保护用户隐私和绕过网络限制。

## ✨ 支持的代理类型

- **HTTP代理** - 标准HTTP代理服务器
- **HTTPS代理** - 加密HTTPS代理服务器  
- **SOCKS5代理** - 支持TCP和UDP的SOCKS5代理
- **SOCKS4代理** - 基础SOCKS4代理协议

## 🚀 使用方法

### 基本语法
```bash
Chat.exe --proxy <proxy_url>
```

### 代理URL格式
```
协议://[用户名:密码@]主机:端口
```

## 📋 使用示例

### 1. HTTP代理
```bash
# 基本HTTP代理
Chat.exe --proxy http://127.0.0.1:8080

# 带认证的HTTP代理
Chat.exe --proxy http://username:<EMAIL>:8080
```

### 2. SOCKS5代理
```bash
# 基本SOCKS5代理
Chat.exe --proxy socks5://127.0.0.1:1080

# 带认证的SOCKS5代理
Chat.exe --proxy socks5://user:<EMAIL>:1080
```

### 3. 组合使用
```bash
# 代理 + 启动URL + 调试模式
Chat.exe --proxy http://127.0.0.1:8080 --url https://web.whatsapp.com --debug

# 代理 + 指纹配置
Chat.exe -p socks5://127.0.0.1:1080 -f fingerprint_001
```

## 🔧 命令行参数详解

### 代理相关参数
| 参数 | 简写 | 说明 | 示例 |
|------|------|------|------|
| `--proxy` | `-p` | 设置代理服务器 | `--proxy http://127.0.0.1:8080` |

### 其他参数
| 参数 | 简写 | 说明 | 示例 |
|------|------|------|------|
| `--url` | `-u` | 启动时打开的URL | `--url https://web.whatsapp.com` |
| `--fingerprint` | `-f` | 指定指纹配置ID | `--fingerprint fp_001` |
| `--debug` | `-d` | 启用调试模式 | `--debug` |
| `--user-data-dir` | | 用户数据目录 | `--user-data-dir C:\MyData` |
| `--cache-dir` | | 缓存目录 | `--cache-dir C:\MyCache` |
| `--help` | `-h` | 显示帮助信息 | `--help` |

## 🛡️ 代理认证

### 支持的认证方式
- **用户名密码认证** - 在URL中包含认证信息
- **自动认证处理** - 应用自动处理代理认证请求

### 认证示例
```bash
# HTTP代理认证
Chat.exe --proxy http://myuser:<EMAIL>:8080

# SOCKS5代理认证
Chat.exe --proxy socks5://username:<EMAIL>:1080
```

## 🔍 代理配置验证

### 自动验证
应用启动时会自动验证代理配置：
- 检查代理URL格式
- 验证主机地址和端口
- 确认代理类型支持

### 错误处理
如果代理配置无效，应用会：
1. 显示错误信息
2. 提供配置建议
3. 安全退出应用

## 📊 代理状态显示

### 状态指示
- **标题栏显示** - 主窗口标题显示当前代理信息
- **调试日志** - 详细的代理配置和连接日志
- **错误提示** - 代理连接失败时的友好提示

### 状态示例
```
聊天集成应用 - 代理: HTTP - 127.0.0.1:8080
聊天集成应用 - 代理: SOCKS5 - <EMAIL>:1080
```

## 🎨 高级功能

### 代理绕过列表
默认绕过以下地址：
- `localhost`
- `127.0.0.1`
- `*.local`

### 自定义绕过
可以通过配置文件自定义绕过列表（未来版本）。

## 🔧 故障排除

### 常见问题

#### Q: 代理连接失败怎么办？
A: 检查以下项目：
1. 代理服务器是否正常运行
2. 网络连接是否正常
3. 代理地址和端口是否正确
4. 用户名密码是否正确

#### Q: 某些网站无法访问？
A: 可能原因：
1. 代理服务器限制了某些网站
2. 网站阻止了代理访问
3. 需要配置代理绕过列表

#### Q: 代理认证失败？
A: 检查项目：
1. 用户名密码是否正确
2. 代理服务器是否支持认证
3. 认证方式是否匹配

### 调试技巧

#### 启用调试模式
```bash
Chat.exe --proxy http://127.0.0.1:8080 --debug
```

#### 查看日志
调试模式下会输出详细的代理配置和连接信息。

## 🌟 最佳实践

### 1. 代理选择
- **HTTP代理** - 适用于基本网页浏览
- **SOCKS5代理** - 推荐用于全协议支持
- **认证代理** - 企业环境或付费代理服务

### 2. 安全考虑
- 使用可信的代理服务器
- 避免在公共网络使用未加密代理
- 定期更换代理服务器

### 3. 性能优化
- 选择地理位置较近的代理服务器
- 避免使用过载的免费代理
- 监控代理连接质量

## 📝 配置文件示例

### 批处理脚本
```batch
@echo off
echo 启动聊天应用（HTTP代理）
Chat.exe --proxy http://127.0.0.1:8080 --url https://web.whatsapp.com

echo 启动聊天应用（SOCKS5代理）
Chat.exe --proxy socks5://user:<EMAIL>:1080 --debug
```

### PowerShell脚本
```powershell
# 启动聊天应用（带代理）
Start-Process "Chat.exe" -ArgumentList "--proxy", "http://127.0.0.1:8080", "--url", "https://web.whatsapp.com"
```

## 🎯 应用场景

### 1. 隐私保护
- 隐藏真实IP地址
- 加密网络流量
- 防止网络监控

### 2. 地理限制绕过
- 访问地区限制的聊天平台
- 绕过网络防火墙
- 访问被屏蔽的服务

### 3. 企业环境
- 通过公司代理访问外网
- 符合企业网络安全政策
- 统一网络出口管理

## 🔄 更新和维护

### 版本兼容性
- 代理功能向后兼容
- 配置格式保持稳定
- 支持热更新代理设置（未来版本）

### 功能扩展
计划中的功能：
- 代理链支持
- 自动代理切换
- 代理性能监控
- 图形化代理配置

现在您可以通过命令行轻松配置代理，享受更安全、更自由的聊天体验！🌐
