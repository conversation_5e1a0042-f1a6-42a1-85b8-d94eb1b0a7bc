using Chat.Models;
using Chat.Services;
using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;

namespace Chat.Views
{
    public partial class FingerprintManagerWindow : Window
    {
        private readonly IBrowserFingerprintService _fingerprintService;
        private List<BrowserFingerprint> _fingerprints;
        private BrowserFingerprint _currentFingerprint;

        public FingerprintManagerWindow()
        {
            InitializeComponent();
            _fingerprintService = new BrowserFingerprintService();
            LoadFingerprints();
        }

        private async void LoadFingerprints()
        {
            try
            {
                _fingerprints = await _fingerprintService.GetAllFingerprintsAsync();
                FingerprintListBox.ItemsSource = _fingerprints;
                
                if (_fingerprints.Count > 0)
                {
                    FingerprintListBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载指纹配置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void FingerprintListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (FingerprintListBox.SelectedItem is BrowserFingerprint fingerprint)
            {
                _currentFingerprint = fingerprint;
                LoadFingerprintDetails(fingerprint);
            }
        }

        private void LoadFingerprintDetails(BrowserFingerprint fingerprint)
        {
            // 基本信息
            FingerprintNameTextBox.Text = fingerprint.Name;
            FingerprintDescriptionTextBox.Text = fingerprint.Description;
            FingerprintEnabledCheckBox.IsChecked = fingerprint.IsEnabled;

            // 浏览器信息
            UserAgentTextBox.Text = fingerprint.UserAgent;
            LanguageTextBox.Text = fingerprint.Language;
            
            // 设置平台
            foreach (ComboBoxItem item in PlatformComboBox.Items)
            {
                if (item.Content.ToString() == fingerprint.Platform)
                {
                    PlatformComboBox.SelectedItem = item;
                    break;
                }
            }

            // 设置时区
            foreach (ComboBoxItem item in TimezoneComboBox.Items)
            {
                if (item.Content.ToString() == fingerprint.TimeZone)
                {
                    TimezoneComboBox.SelectedItem = item;
                    break;
                }
            }

            // 设置Do Not Track
            foreach (ComboBoxItem item in DoNotTrackComboBox.Items)
            {
                if (item.Tag.ToString() == fingerprint.DoNotTrack)
                {
                    DoNotTrackComboBox.SelectedItem = item;
                    break;
                }
            }

            // 屏幕信息
            ScreenWidthTextBox.Text = fingerprint.Screen.Width.ToString();
            ScreenHeightTextBox.Text = fingerprint.Screen.Height.ToString();
            ColorDepthComboBox.Text = fingerprint.Screen.ColorDepth.ToString();
            DevicePixelRatioTextBox.Text = fingerprint.Screen.DevicePixelRatio.ToString("F1");

            // 硬件信息
            CpuCoresComboBox.Text = fingerprint.Hardware.CpuCores.ToString();
            MemoryComboBox.Text = fingerprint.Hardware.Memory.ToString("F0");
            DeviceNameTextBox.Text = fingerprint.Hardware.DeviceName;

            // 高级选项
            CanvasNoiseComboBox.Text = fingerprint.Canvas.NoiseLevel;
            AudioNoiseComboBox.Text = fingerprint.Audio.NoiseLevel;
            WebRTCEnabledCheckBox.IsChecked = fingerprint.WebRTC.Enabled;
            BlockLocalIPCheckBox.IsChecked = fingerprint.WebRTC.BlockLocalIP;
            BlockPublicIPCheckBox.IsChecked = fingerprint.WebRTC.BlockPublicIP;
        }

        private void SaveFingerprintFromUI()
        {
            if (_currentFingerprint == null) return;

            try
            {
                // 基本信息
                _currentFingerprint.Name = FingerprintNameTextBox.Text;
                _currentFingerprint.Description = FingerprintDescriptionTextBox.Text;
                _currentFingerprint.IsEnabled = FingerprintEnabledCheckBox.IsChecked ?? false;

                // 浏览器信息
                _currentFingerprint.UserAgent = UserAgentTextBox.Text;
                _currentFingerprint.Language = LanguageTextBox.Text;
                _currentFingerprint.Platform = (PlatformComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "Win32";
                _currentFingerprint.TimeZone = (TimezoneComboBox.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "Asia/Shanghai";
                _currentFingerprint.DoNotTrack = (DoNotTrackComboBox.SelectedItem as ComboBoxItem)?.Tag.ToString() ?? "1";

                // 时区偏移
                var selectedTimezone = TimezoneComboBox.SelectedItem as ComboBoxItem;
                if (selectedTimezone?.Tag != null && int.TryParse(selectedTimezone.Tag.ToString(), out int offset))
                {
                    _currentFingerprint.TimezoneOffset = offset;
                }

                // 屏幕信息
                if (int.TryParse(ScreenWidthTextBox.Text, out int width))
                    _currentFingerprint.Screen.Width = width;
                if (int.TryParse(ScreenHeightTextBox.Text, out int height))
                    _currentFingerprint.Screen.Height = height;
                if (int.TryParse(ColorDepthComboBox.Text, out int colorDepth))
                    _currentFingerprint.Screen.ColorDepth = colorDepth;
                if (double.TryParse(DevicePixelRatioTextBox.Text, out double pixelRatio))
                    _currentFingerprint.Screen.DevicePixelRatio = pixelRatio;

                // 硬件信息
                if (int.TryParse(CpuCoresComboBox.Text, out int cpuCores))
                    _currentFingerprint.Hardware.CpuCores = cpuCores;
                if (double.TryParse(MemoryComboBox.Text, out double memory))
                    _currentFingerprint.Hardware.Memory = memory;
                _currentFingerprint.Hardware.DeviceName = DeviceNameTextBox.Text;

                // 高级选项
                _currentFingerprint.Canvas.NoiseLevel = CanvasNoiseComboBox.Text;
                _currentFingerprint.Audio.NoiseLevel = AudioNoiseComboBox.Text;
                _currentFingerprint.WebRTC.Enabled = WebRTCEnabledCheckBox.IsChecked ?? true;
                _currentFingerprint.WebRTC.BlockLocalIP = BlockLocalIPCheckBox.IsChecked ?? false;
                _currentFingerprint.WebRTC.BlockPublicIP = BlockPublicIPCheckBox.IsChecked ?? false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存指纹数据时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void NewFingerprintButton_Click(object sender, RoutedEventArgs e)
        {
            var newFingerprint = _fingerprintService.CreateDefaultFingerprint();
            newFingerprint.Name = $"新指纹_{DateTime.Now:yyyyMMdd_HHmmss}";
            
            if (await _fingerprintService.SaveFingerprintAsync(newFingerprint))
            {
                _fingerprints.Add(newFingerprint);
                FingerprintListBox.Items.Refresh();
                FingerprintListBox.SelectedItem = newFingerprint;
                MessageBox.Show("新指纹配置已创建", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("创建指纹配置失败", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void RandomFingerprintButton_Click(object sender, RoutedEventArgs e)
        {
            var randomFingerprint = _fingerprintService.GenerateRandomFingerprint();
            
            if (await _fingerprintService.SaveFingerprintAsync(randomFingerprint))
            {
                _fingerprints.Add(randomFingerprint);
                FingerprintListBox.Items.Refresh();
                FingerprintListBox.SelectedItem = randomFingerprint;
                MessageBox.Show("随机指纹配置已生成", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("生成随机指纹配置失败", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void ImportButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "JSON文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                Title = "选择要导入的指纹配置文件"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                if (await _fingerprintService.ImportFingerprintAsync(openFileDialog.FileName))
                {
                    LoadFingerprints();
                    MessageBox.Show("指纹配置导入成功", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("导入指纹配置失败", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentFingerprint == null)
            {
                MessageBox.Show("请先选择要导出的指纹配置", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var saveFileDialog = new SaveFileDialog
            {
                Filter = "JSON文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                Title = "保存指纹配置文件",
                FileName = $"{_currentFingerprint.Name}.json"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                if (await _fingerprintService.ExportFingerprintAsync(_currentFingerprint, saveFileDialog.FileName))
                {
                    MessageBox.Show("指纹配置导出成功", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("导出指纹配置失败", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void TestFingerprintButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentFingerprint == null)
            {
                MessageBox.Show("请先选择要测试的指纹配置", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // 打开指纹测试窗口
            var testWindow = new FingerprintTestWindow(_currentFingerprint);
            testWindow.Show();
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentFingerprint == null)
            {
                MessageBox.Show("请先选择要保存的指纹配置", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            SaveFingerprintFromUI();

            if (await _fingerprintService.SaveFingerprintAsync(_currentFingerprint))
            {
                FingerprintListBox.Items.Refresh();
                MessageBox.Show("指纹配置保存成功", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("保存指纹配置失败", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentFingerprint == null)
            {
                MessageBox.Show("请先选择要删除的指纹配置", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"确定要删除指纹配置 '{_currentFingerprint.Name}' 吗？", 
                "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                if (await _fingerprintService.DeleteFingerprintAsync(_currentFingerprint.Id))
                {
                    _fingerprints.Remove(_currentFingerprint);
                    FingerprintListBox.Items.Refresh();
                    _currentFingerprint = null;
                    MessageBox.Show("指纹配置删除成功", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("删除指纹配置失败", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }

    // 转换器类
    public class BoolToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isEnabled)
            {
                return isEnabled ? Colors.Green : Colors.Red;
            }
            return Colors.Gray;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class BoolToStatusConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isEnabled)
            {
                return isEnabled ? "已启用" : "已禁用";
            }
            return "未知";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
