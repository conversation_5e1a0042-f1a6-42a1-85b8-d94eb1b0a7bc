using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using ChatLauncher.Models;
using ChatLauncher.Services;

namespace ChatLauncher
{
    public partial class MainWindow : HandyControl.Controls.Window
    {
        private readonly ChatAppLauncher _launcher;
        private readonly ObservableCollection<LaunchConfig> _configs;
        private readonly ObservableCollection<LaunchConfig> _filteredConfigs;
        private readonly DispatcherTimer _statusTimer;

        public MainWindow()
        {
            InitializeComponent();
            
            _launcher = new ChatAppLauncher();
            _configs = new ObservableCollection<LaunchConfig>();
            _filteredConfigs = new ObservableCollection<LaunchConfig>();
            
            ConfigListBox.ItemsSource = _filteredConfigs;
            
            // Initialize status update timer
            _statusTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(2)
            };
            _statusTimer.Tick += StatusTimer_Tick;
            _statusTimer.Start();
            
            LoadConfigs();
            UpdateStatus();
        }

        private void LoadConfigs()
        {
            try
            {
                // Load predefined configurations
                var defaultConfigs = PredefinedConfigs.GetDefaultConfigs();
                _configs.Clear();
                
                foreach (var config in defaultConfigs)
                {
                    _configs.Add(config);
                }

                // TODO: Load user custom configurations from file
                
                FilterConfigs();
                
                if (_filteredConfigs.Count > 0)
                {
                    ConfigListBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Failed to load configurations: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void FilterConfigs()
        {
            _filteredConfigs.Clear();
            
            string searchText = SearchBox.Text?.ToLower() ?? "";
            
            var filtered = string.IsNullOrEmpty(searchText) 
                ? _configs 
                : _configs.Where(c => c.Name.ToLower().Contains(searchText) || 
                                     c.Description?.ToLower().Contains(searchText) == true);
            
            foreach (var config in filtered.OrderByDescending(c => c.IsFavorite)
                                          .ThenByDescending(c => c.UseCount)
                                          .ThenBy(c => c.Name))
            {
                _filteredConfigs.Add(config);
            }
        }

        private async void UpdateStatus()
        {
            try
            {
                // Check Chat application status
                bool isAvailable = _launcher.IsChatAppAvailable();
                string version = _launcher.GetChatAppVersion();
                string path = _launcher.GetChatAppPath();
                
                AppStatusText.Text = isAvailable 
                    ? $"Chat App: Available (v{version})" 
                    : "Chat App: Not Available";
                
                StatusText.Text = isAvailable 
                    ? $"Ready - {Path.GetFileName(path)}" 
                    : "Chat.exe not found";
                
                // Check running instances
                var instances = _launcher.GetRunningChatInstances();
                RunningInstancesText.Text = instances.Length.ToString();
                
                // Update button states
                LaunchButton.IsEnabled = isAvailable && ConfigListBox.SelectedItem != null;
                CloseAllButton.IsEnabled = instances.Length > 0;
            }
            catch (Exception ex)
            {
                StatusText.Text = $"Status update failed: {ex.Message}";
            }
        }

        private void StatusTimer_Tick(object sender, EventArgs e)
        {
            UpdateStatus();
        }

        private async void LaunchButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedConfig = ConfigListBox.SelectedItem as LaunchConfig;
            if (selectedConfig == null)
            {
                System.Windows.MessageBox.Show("Please select a configuration", "Info",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                LaunchButton.IsEnabled = false;
                StatusText.Text = "Launching...";

                if (TestModeCheckBox.IsChecked == true)
                {
                    // Test mode
                    bool testResult = _launcher.TestLaunch(selectedConfig, out string commandLine);
                    CommandLinePreview.Text = commandLine;
                    
                    System.Windows.MessageBox.Show(testResult ? "Configuration test passed!" : "Configuration test failed!",
                        "Test Result", MessageBoxButton.OK,
                        testResult ? MessageBoxImage.Information : MessageBoxImage.Warning);
                }
                else if (MultiInstanceCheckBox.IsChecked == true)
                {
                    // Multi-instance launch
                    if (int.TryParse(InstanceCountTextBox.Text, out int instanceCount) && instanceCount > 0)
                    {
                        int successCount = await _launcher.LaunchMultipleAsync(selectedConfig, instanceCount);
                        System.Windows.MessageBox.Show($"Successfully launched {successCount}/{instanceCount} instances",
                            "Launch Complete", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        System.Windows.MessageBox.Show("Please enter a valid instance count", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    // Single instance launch
                    bool success = await _launcher.LaunchAsync(selectedConfig);
                    if (success)
                    {
                        StatusText.Text = "Launch successful";
                        // Refresh config list to update usage statistics
                        FilterConfigs();
                    }
                    else
                    {
                        StatusText.Text = "Launch failed";
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Launch failed: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "Launch failed";
            }
            finally
            {
                LaunchButton.IsEnabled = true;
            }
        }

        private void ConfigListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var selectedConfig = ConfigListBox.SelectedItem as LaunchConfig;
            if (selectedConfig != null)
            {
                // Update configuration details display
                ConfigNameText.Text = selectedConfig.Name;
                ConfigUrlText.Text = selectedConfig.Url ?? "None";
                ConfigProxyText.Text = selectedConfig.Proxy ?? "None";
                ConfigDebugText.Text = selectedConfig.Debug ? "Yes" : "No";
                ConfigUseCountText.Text = selectedConfig.UseCount.ToString();
                
                // Update command line preview
                UpdateCommandLinePreview(selectedConfig);
                
                // Update button states
                EditConfigButton.IsEnabled = true;
                DeleteConfigButton.IsEnabled = !IsPredefinedConfig(selectedConfig);
            }
            else
            {
                // Clear display
                ConfigNameText.Text = "";
                ConfigUrlText.Text = "";
                ConfigProxyText.Text = "";
                ConfigDebugText.Text = "";
                ConfigUseCountText.Text = "";
                CommandLinePreview.Text = "";
                
                EditConfigButton.IsEnabled = false;
                DeleteConfigButton.IsEnabled = false;
            }
        }

        private void UpdateCommandLinePreview(LaunchConfig config)
        {
            try
            {
                if (_launcher.TestLaunch(config, out string commandLine))
                {
                    CommandLinePreview.Text = commandLine;
                }
                else
                {
                    CommandLinePreview.Text = commandLine; // Error message
                }
            }
            catch (Exception ex)
            {
                CommandLinePreview.Text = $"Preview failed: {ex.Message}";
            }
        }

        private bool IsPredefinedConfig(LaunchConfig config)
        {
            // Simple check: predefined configs have default creation time
            return config.CreatedTime.Date == DateTime.Now.Date && config.UseCount == 0;
        }

        private void SearchBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterConfigs();
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadConfigs();
            UpdateStatus();
            StatusText.Text = "Refreshed";
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            System.Windows.MessageBox.Show("Settings feature under development...", "Info",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AddConfigButton_Click(object sender, RoutedEventArgs e)
        {
            System.Windows.MessageBox.Show("Add configuration feature under development...", "Info",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void EditConfigButton_Click(object sender, RoutedEventArgs e)
        {
            System.Windows.MessageBox.Show("Edit configuration feature under development...", "Info",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void DeleteConfigButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedConfig = ConfigListBox.SelectedItem as LaunchConfig;
            if (selectedConfig != null && !IsPredefinedConfig(selectedConfig))
            {
                var result = System.Windows.MessageBox.Show($"Are you sure you want to delete configuration '{selectedConfig.Name}'?",
                    "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    _configs.Remove(selectedConfig);
                    FilterConfigs();
                    StatusText.Text = "Configuration deleted";
                }
            }
        }

        private void ViewLogsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string logDir = "logs";
                if (Directory.Exists(logDir))
                {
                    Process.Start("explorer.exe", logDir);
                }
                else
                {
                    System.Windows.MessageBox.Show("Log directory does not exist", "Info",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Failed to open log directory: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void CloseAllButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var instances = _launcher.GetRunningChatInstances();
                if (instances.Length == 0)
                {
                    System.Windows.MessageBox.Show("No running Chat instances", "Info",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var result = System.Windows.MessageBox.Show($"Are you sure you want to close all {instances.Length} Chat instances?",
                    "Confirm Close", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    CloseAllButton.IsEnabled = false;
                    StatusText.Text = "Closing instances...";
                    
                    int closedCount = _launcher.CloseAllChatInstances();
                    
                    System.Windows.MessageBox.Show($"Closed {closedCount} instances", "Complete",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    StatusText.Text = "Instances closed";
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Failed to close instances: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                CloseAllButton.IsEnabled = true;
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _statusTimer?.Stop();
            base.OnClosed(e);
        }
    }
}
