using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using ChatLauncher.Models;
using ChatLauncher.Services;

namespace ChatLauncher
{
    public partial class MainWindow : Window
    {
        private readonly ChatAppLauncher _launcher;
        private readonly ObservableCollection<LaunchConfig> _configs;
        private readonly ObservableCollection<LaunchConfig> _filteredConfigs;
        private readonly DispatcherTimer _statusTimer;

        public MainWindow()
        {
            InitializeComponent();
            
            _launcher = new ChatAppLauncher();
            _configs = new ObservableCollection<LaunchConfig>();
            _filteredConfigs = new ObservableCollection<LaunchConfig>();
            
            ConfigListBox.ItemsSource = _filteredConfigs;
            
            // 初始化状态更新定时器
            _statusTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(2)
            };
            _statusTimer.Tick += StatusTimer_Tick;
            _statusTimer.Start();
            
            LoadConfigs();
            UpdateStatus();
        }

        private void LoadConfigs()
        {
            try
            {
                // 加载预定义配置
                var defaultConfigs = PredefinedConfigs.GetDefaultConfigs();
                _configs.Clear();
                
                foreach (var config in defaultConfigs)
                {
                    _configs.Add(config);
                }

                // TODO: 从文件加载用户自定义配置
                
                FilterConfigs();
                
                if (_filteredConfigs.Count > 0)
                {
                    ConfigListBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载配置失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void FilterConfigs()
        {
            _filteredConfigs.Clear();
            
            string searchText = SearchBox.Text?.ToLower() ?? "";
            
            var filtered = string.IsNullOrEmpty(searchText) 
                ? _configs 
                : _configs.Where(c => c.Name.ToLower().Contains(searchText) || 
                                     c.Description?.ToLower().Contains(searchText) == true);
            
            foreach (var config in filtered.OrderByDescending(c => c.IsFavorite)
                                          .ThenByDescending(c => c.UseCount)
                                          .ThenBy(c => c.Name))
            {
                _filteredConfigs.Add(config);
            }
        }

        private async void UpdateStatus()
        {
            try
            {
                // 检查Chat应用状态
                bool isAvailable = _launcher.IsChatAppAvailable();
                string version = _launcher.GetChatAppVersion();
                string path = _launcher.GetChatAppPath();
                
                AppStatusText.Text = isAvailable 
                    ? $"Chat应用：可用 (v{version})" 
                    : "Chat应用：不可用";
                
                StatusText.Text = isAvailable 
                    ? $"就绪 - {Path.GetFileName(path)}" 
                    : "Chat.exe未找到";
                
                // 检查运行实例
                var instances = _launcher.GetRunningChatInstances();
                RunningInstancesText.Text = $"运行实例：{instances.Length}";
                
                // 更新按钮状态
                LaunchButton.IsEnabled = isAvailable && ConfigListBox.SelectedItem != null;
                CloseAllButton.IsEnabled = instances.Length > 0;
            }
            catch (Exception ex)
            {
                StatusText.Text = $"状态更新失败: {ex.Message}";
            }
        }

        private void StatusTimer_Tick(object sender, EventArgs e)
        {
            UpdateStatus();
        }

        private async void LaunchButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedConfig = ConfigListBox.SelectedItem as LaunchConfig;
            if (selectedConfig == null)
            {
                MessageBox.Show("请选择一个配置", "提示", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                LaunchButton.IsEnabled = false;
                StatusText.Text = "正在启动...";

                if (TestModeCheckBox.IsChecked == true)
                {
                    // 测试模式
                    bool testResult = _launcher.TestLaunch(selectedConfig, out string commandLine);
                    CommandLinePreview.Text = commandLine;
                    
                    MessageBox.Show(testResult ? "配置测试通过！" : "配置测试失败！", 
                        "测试结果", MessageBoxButton.OK, 
                        testResult ? MessageBoxImage.Information : MessageBoxImage.Warning);
                }
                else if (MultiInstanceCheckBox.IsChecked == true)
                {
                    // 多实例启动
                    if (int.TryParse(InstanceCountTextBox.Text, out int instanceCount) && instanceCount > 0)
                    {
                        int successCount = await _launcher.LaunchMultipleAsync(selectedConfig, instanceCount);
                        MessageBox.Show($"成功启动 {successCount}/{instanceCount} 个实例", 
                            "启动完成", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("请输入有效的实例数量", "错误", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    // 单实例启动
                    bool success = await _launcher.LaunchAsync(selectedConfig);
                    if (success)
                    {
                        StatusText.Text = "启动成功";
                        // 刷新配置列表以更新使用统计
                        FilterConfigs();
                    }
                    else
                    {
                        StatusText.Text = "启动失败";
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "启动失败";
            }
            finally
            {
                LaunchButton.IsEnabled = true;
            }
        }

        private void ConfigListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var selectedConfig = ConfigListBox.SelectedItem as LaunchConfig;
            if (selectedConfig != null)
            {
                // 更新配置详情显示
                ConfigNameText.Text = selectedConfig.Name;
                ConfigUrlText.Text = selectedConfig.Url ?? "无";
                ConfigProxyText.Text = selectedConfig.Proxy ?? "无";
                ConfigDebugText.Text = selectedConfig.Debug ? "是" : "否";
                ConfigUseCountText.Text = selectedConfig.UseCount.ToString();
                
                // 更新命令行预览
                UpdateCommandLinePreview(selectedConfig);
                
                // 更新按钮状态
                EditConfigButton.IsEnabled = true;
                DeleteConfigButton.IsEnabled = !IsPredefinedConfig(selectedConfig);
            }
            else
            {
                // 清空显示
                ConfigNameText.Text = "";
                ConfigUrlText.Text = "";
                ConfigProxyText.Text = "";
                ConfigDebugText.Text = "";
                ConfigUseCountText.Text = "";
                CommandLinePreview.Text = "";
                
                EditConfigButton.IsEnabled = false;
                DeleteConfigButton.IsEnabled = false;
            }
        }

        private void UpdateCommandLinePreview(LaunchConfig config)
        {
            try
            {
                if (_launcher.TestLaunch(config, out string commandLine))
                {
                    CommandLinePreview.Text = commandLine;
                }
                else
                {
                    CommandLinePreview.Text = commandLine; // 错误信息
                }
            }
            catch (Exception ex)
            {
                CommandLinePreview.Text = $"预览失败: {ex.Message}";
            }
        }

        private bool IsPredefinedConfig(LaunchConfig config)
        {
            // 简单判断：预定义配置的创建时间为默认值
            return config.CreatedTime.Date == DateTime.Now.Date && config.UseCount == 0;
        }

        private void SearchBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterConfigs();
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadConfigs();
            UpdateStatus();
            StatusText.Text = "已刷新";
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("设置功能开发中...", "提示", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AddConfigButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("新建配置功能开发中...", "提示", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void EditConfigButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("编辑配置功能开发中...", "提示", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void DeleteConfigButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedConfig = ConfigListBox.SelectedItem as LaunchConfig;
            if (selectedConfig != null && !IsPredefinedConfig(selectedConfig))
            {
                var result = MessageBox.Show($"确定要删除配置 '{selectedConfig.Name}' 吗？", 
                    "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    _configs.Remove(selectedConfig);
                    FilterConfigs();
                    StatusText.Text = "配置已删除";
                }
            }
        }

        private void ViewLogsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string logDir = "logs";
                if (Directory.Exists(logDir))
                {
                    Process.Start("explorer.exe", logDir);
                }
                else
                {
                    MessageBox.Show("日志目录不存在", "提示", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开日志目录失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void CloseAllButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var instances = _launcher.GetRunningChatInstances();
                if (instances.Length == 0)
                {
                    MessageBox.Show("没有运行中的Chat实例", "提示", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var result = MessageBox.Show($"确定要关闭所有 {instances.Length} 个Chat实例吗？", 
                    "确认关闭", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    CloseAllButton.IsEnabled = false;
                    StatusText.Text = "正在关闭实例...";
                    
                    int closedCount = _launcher.CloseAllChatInstances();
                    
                    MessageBox.Show($"已关闭 {closedCount} 个实例", "完成", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    StatusText.Text = "实例已关闭";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"关闭实例失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                CloseAllButton.IsEnabled = true;
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _statusTimer?.Stop();
            base.OnClosed(e);
        }
    }
}
