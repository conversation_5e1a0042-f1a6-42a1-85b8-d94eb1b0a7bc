using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using ChatLauncher.Models;
using ChatLauncher.Services;

namespace ChatLauncher
{
    public partial class MainWindow : HandyControl.Controls.Window
    {
        private readonly ChatAppLauncher _launcher;
        private readonly ConfigurationManager _configManager;
        private readonly ObservableCollection<LaunchConfig> _configs;
        private readonly ObservableCollection<LaunchConfig> _filteredConfigs;
        private readonly DispatcherTimer _statusTimer;

        public MainWindow()
        {
            InitializeComponent();

            _launcher = new ChatAppLauncher();
            _configManager = new ConfigurationManager();
            _configs = new ObservableCollection<LaunchConfig>();
            _filteredConfigs = new ObservableCollection<LaunchConfig>();

            ConfigListBox.ItemsSource = _filteredConfigs;
            
            // Initialize status update timer
            _statusTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(2)
            };
            _statusTimer.Tick += StatusTimer_Tick;
            _statusTimer.Start();
            
            LoadConfigs();
            UpdateStatus();
        }

        private void LoadConfigs()
        {
            try
            {
                // Load all configurations from configuration manager
                var allConfigs = _configManager.LoadConfigs();
                _configs.Clear();

                foreach (var config in allConfigs)
                {
                    _configs.Add(config);
                }

                FilterConfigs();

                // 调试信息
                System.Diagnostics.Debug.WriteLine($"LoadConfigs: 加载了 {_configs.Count} 个配置，过滤后显示 {_filteredConfigs.Count} 个");

                if (_filteredConfigs.Count > 0 && ConfigListBox.SelectedItem == null)
                {
                    ConfigListBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Failed to load configurations: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 强制刷新配置列表
        /// </summary>
        private void ForceRefreshConfigs()
        {
            // 保存当前选中的配置名称
            string selectedConfigName = (ConfigListBox.SelectedItem as LaunchConfig)?.Name;

            // 重新加载配置
            LoadConfigs();

            // 尝试恢复选中状态
            if (!string.IsNullOrEmpty(selectedConfigName))
            {
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    var configToSelect = _filteredConfigs.FirstOrDefault(c => c.Name == selectedConfigName);
                    if (configToSelect != null)
                    {
                        ConfigListBox.SelectedItem = configToSelect;
                        ConfigListBox.ScrollIntoView(configToSelect);
                    }
                }), System.Windows.Threading.DispatcherPriority.Background);
            }
        }

        private void FilterConfigs()
        {
            _filteredConfigs.Clear();
            
            string searchText = SearchBox.Text?.ToLower() ?? "";
            
            var filtered = string.IsNullOrEmpty(searchText) 
                ? _configs 
                : _configs.Where(c => c.Name.ToLower().Contains(searchText) || 
                                     c.Description?.ToLower().Contains(searchText) == true);
            
            foreach (var config in filtered.OrderByDescending(c => c.IsFavorite)
                                          .ThenByDescending(c => c.UseCount)
                                          .ThenBy(c => c.Name))
            {
                _filteredConfigs.Add(config);
            }
        }

        private async void UpdateStatus()
        {
            try
            {
                // Check Chat application status
                bool isAvailable = _launcher.IsChatAppAvailable();
                string version = _launcher.GetChatAppVersion();
                string path = _launcher.GetChatAppPath();
                
                AppStatusText.Text = isAvailable 
                    ? $"Chat App: Available (v{version})" 
                    : "Chat App: Not Available";
                
                StatusText.Text = isAvailable 
                    ? $"Ready - {Path.GetFileName(path)}" 
                    : "Chat.exe not found";
                
                // Check running instances
                var instances = _launcher.GetRunningChatInstances();
                RunningInstancesText.Text = instances.Length.ToString();
                
                // Update button states
                LaunchButton.IsEnabled = isAvailable && ConfigListBox.SelectedItem != null;
                CloseAllButton.IsEnabled = instances.Length > 0;
            }
            catch (Exception ex)
            {
                StatusText.Text = $"Status update failed: {ex.Message}";
            }
        }

        private void StatusTimer_Tick(object sender, EventArgs e)
        {
            UpdateStatus();
        }

        private async void LaunchButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedConfig = ConfigListBox.SelectedItem as LaunchConfig;
            if (selectedConfig == null)
            {
                System.Windows.MessageBox.Show("Please select a configuration", "Info",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                LaunchButton.IsEnabled = false;
                StatusText.Text = "Launching...";

                if (TestModeCheckBox.IsChecked == true)
                {
                    // Test mode
                    bool testResult = _launcher.TestLaunch(selectedConfig, out string commandLine);
                    CommandLinePreview.Text = commandLine;
                    
                    System.Windows.MessageBox.Show(testResult ? "Configuration test passed!" : "Configuration test failed!",
                        "Test Result", MessageBoxButton.OK,
                        testResult ? MessageBoxImage.Information : MessageBoxImage.Warning);
                }
                else if (MultiInstanceCheckBox.IsChecked == true)
                {
                    // Multi-instance launch
                    if (int.TryParse(InstanceCountTextBox.Text, out int instanceCount) && instanceCount > 0)
                    {
                        int successCount = await _launcher.LaunchMultipleAsync(selectedConfig, instanceCount);
                        System.Windows.MessageBox.Show($"Successfully launched {successCount}/{instanceCount} instances",
                            "Launch Complete", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        System.Windows.MessageBox.Show("Please enter a valid instance count", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    // Single instance launch
                    bool success = await _launcher.LaunchAsync(selectedConfig);
                    if (success)
                    {
                        StatusText.Text = "Launch successful";
                        // Refresh config list to update usage statistics
                        FilterConfigs();
                    }
                    else
                    {
                        StatusText.Text = "Launch failed";
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Launch failed: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "Launch failed";
            }
            finally
            {
                LaunchButton.IsEnabled = true;
            }
        }

        private void ConfigListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var selectedConfig = ConfigListBox.SelectedItem as LaunchConfig;
            if (selectedConfig != null)
            {
                // Update configuration details display
                ConfigNameText.Text = selectedConfig.Name ?? "";
                ConfigUrlText.Text = string.IsNullOrEmpty(selectedConfig.Url) ? "无" : selectedConfig.Url;
                ConfigProxyText.Text = string.IsNullOrEmpty(selectedConfig.Proxy) ? "无" : selectedConfig.Proxy;
                ConfigDebugText.Text = selectedConfig.Debug ? "是" : "否";
                ConfigUseCountText.Text = selectedConfig.UseCount.ToString();
                
                // Update command line preview
                UpdateCommandLinePreview(selectedConfig);
                
                // Update button states
                EditConfigButton.IsEnabled = true;
                DeleteConfigButton.IsEnabled = !IsPredefinedConfig(selectedConfig);
            }
            else
            {
                // Clear display
                ConfigNameText.Text = "";
                ConfigUrlText.Text = "";
                ConfigProxyText.Text = "";
                ConfigDebugText.Text = "";
                ConfigUseCountText.Text = "";
                CommandLinePreview.Text = "";
                
                EditConfigButton.IsEnabled = false;
                DeleteConfigButton.IsEnabled = false;
            }
        }

        private void UpdateCommandLinePreview(LaunchConfig config)
        {
            try
            {
                if (_launcher.TestLaunch(config, out string commandLine))
                {
                    CommandLinePreview.Text = commandLine;
                }
                else
                {
                    CommandLinePreview.Text = commandLine; // Error message
                }
            }
            catch (Exception ex)
            {
                CommandLinePreview.Text = $"Preview failed: {ex.Message}";
            }
        }

        private bool IsPredefinedConfig(LaunchConfig config)
        {
            return config.IsDefault;
        }

        private void SearchBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterConfigs();
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadConfigs();
            UpdateStatus();
            StatusText.Text = "Refreshed";
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            System.Windows.MessageBox.Show("Settings feature under development...", "Info",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AddConfigButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var editWindow = new Views.ConfigEditWindow();
                editWindow.Owner = this;

                if (editWindow.ShowDialog() == true)
                {
                    var newConfig = editWindow.Config;

                    // 检查名称是否重复
                    if (_configs.Any(c => c.Name == newConfig.Name))
                    {
                        System.Windows.MessageBox.Show($"配置名称 '{newConfig.Name}' 已存在，请使用其他名称。",
                            "名称重复", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    // 保存配置
                    _configManager.SaveConfig(newConfig);

                    // 强制刷新列表
                    ForceRefreshConfigs();

                    // 使用Dispatcher确保界面更新完成后再选中配置
                    Dispatcher.BeginInvoke(new Action(() =>
                    {
                        // 选中新添加的配置
                        var addedConfig = _filteredConfigs.FirstOrDefault(c => c.Name == newConfig.Name);
                        if (addedConfig != null)
                        {
                            ConfigListBox.SelectedItem = addedConfig;
                            ConfigListBox.ScrollIntoView(addedConfig);
                        }
                    }), System.Windows.Threading.DispatcherPriority.Background);

                    StatusText.Text = $"配置 '{newConfig.Name}' 已添加";
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"添加配置时发生错误：{ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void EditConfigButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedConfig = ConfigListBox.SelectedItem as LaunchConfig;
            if (selectedConfig == null)
            {
                return;
            }

            try
            {
                var editWindow = new Views.ConfigEditWindow(selectedConfig);
                editWindow.Owner = this;

                if (editWindow.ShowDialog() == true)
                {
                    var editedConfig = editWindow.Config;

                    // 检查名称是否与其他配置重复（排除自己）
                    if (_configs.Any(c => c.Name == editedConfig.Name && c != selectedConfig))
                    {
                        System.Windows.MessageBox.Show($"配置名称 '{editedConfig.Name}' 已存在，请使用其他名称。",
                            "名称重复", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    if (selectedConfig.IsDefault)
                    {
                        // 默认配置不能直接修改，创建为用户配置
                        editedConfig.IsDefault = false;
                        editedConfig.CreatedTime = DateTime.Now;
                        editedConfig.UseCount = 0;
                        _configManager.SaveConfig(editedConfig);
                        StatusText.Text = $"已基于默认配置创建新配置 '{editedConfig.Name}'";

                        // 调试信息
                        System.Diagnostics.Debug.WriteLine($"创建基于默认配置的用户配置: {editedConfig.Name}, Proxy: {editedConfig.Proxy}");
                    }
                    else
                    {
                        // 更新用户配置
                        _configManager.SaveConfig(editedConfig);
                        StatusText.Text = $"配置 '{editedConfig.Name}' 已更新";

                        // 调试信息
                        System.Diagnostics.Debug.WriteLine($"更新用户配置: {editedConfig.Name}, Proxy: {editedConfig.Proxy}");
                    }

                    // 强制刷新列表
                    ForceRefreshConfigs();

                    // 使用Dispatcher确保界面更新完成后再选中配置
                    Dispatcher.BeginInvoke(new Action(() =>
                    {
                        // 选中编辑的配置
                        var updatedConfig = _filteredConfigs.FirstOrDefault(c => c.Name == editedConfig.Name);
                        if (updatedConfig != null)
                        {
                            ConfigListBox.SelectedItem = updatedConfig;
                            ConfigListBox.ScrollIntoView(updatedConfig);

                            // 调试信息
                            System.Diagnostics.Debug.WriteLine($"选中配置: {updatedConfig.Name}, 列表中共有 {_filteredConfigs.Count} 个配置");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"未找到配置: {editedConfig.Name}, 列表中的配置: {string.Join(", ", _filteredConfigs.Select(c => c.Name))}");
                        }
                    }), System.Windows.Threading.DispatcherPriority.Background);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"编辑配置时发生错误：{ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DeleteConfigButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedConfig = ConfigListBox.SelectedItem as LaunchConfig;
            if (selectedConfig == null || selectedConfig.IsDefault)
            {
                return;
            }

            try
            {
                var result = System.Windows.MessageBox.Show($"确定要删除配置 '{selectedConfig.Name}' 吗？\n\n此操作无法撤销。",
                    "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    _configManager.DeleteConfig(selectedConfig);
                    LoadConfigs();
                    StatusText.Text = $"配置 '{selectedConfig.Name}' 已删除";
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"删除配置时发生错误：{ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewLogsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string logDir = "logs";
                if (Directory.Exists(logDir))
                {
                    Process.Start("explorer.exe", logDir);
                }
                else
                {
                    System.Windows.MessageBox.Show("Log directory does not exist", "Info",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Failed to open log directory: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void CloseAllButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var instances = _launcher.GetRunningChatInstances();
                if (instances.Length == 0)
                {
                    System.Windows.MessageBox.Show("No running Chat instances", "Info",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var result = System.Windows.MessageBox.Show($"Are you sure you want to close all {instances.Length} Chat instances?",
                    "Confirm Close", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    CloseAllButton.IsEnabled = false;
                    StatusText.Text = "Closing instances...";
                    
                    int closedCount = _launcher.CloseAllChatInstances();
                    
                    System.Windows.MessageBox.Show($"Closed {closedCount} instances", "Complete",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    StatusText.Text = "Instances closed";
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Failed to close instances: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                CloseAllButton.IsEnabled = true;
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _statusTimer?.Stop();
            base.OnClosed(e);
        }
    }
}
