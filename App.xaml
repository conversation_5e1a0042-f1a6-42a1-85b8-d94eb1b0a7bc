﻿<Application x:Class="ChatLauncher.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- HandyControl主题 -->
                <hc:Theme/>
                <!-- 自定义样式 -->
                <ResourceDictionary>
                    <!-- 自定义颜色 -->
                    <SolidColorBrush x:Key="PrimaryBrush" Color="#FF007ACC"/>
                    <SolidColorBrush x:Key="DarkPrimaryBrush" Color="#FF005A9E"/>
                    <SolidColorBrush x:Key="LightPrimaryBrush" Color="#FF4A9EFF"/>

                    <!-- 选中状态颜色 -->
                    <SolidColorBrush x:Key="SelectedBorderBrush" Color="#FF007ACC"/>
                    <SolidColorBrush x:Key="SelectedBackgroundBrush" Color="#1A007ACC"/>
                    <SolidColorBrush x:Key="HoverBorderBrush" Color="#FF4A9EFF"/>
                    <SolidColorBrush x:Key="HoverBackgroundBrush" Color="#0A4A9EFF"/>

                    <!-- 自定义按钮样式 -->
                    <Style x:Key="LauncherButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonPrimary}">
                        <Setter Property="Margin" Value="10"/>
                        <Setter Property="Padding" Value="28,16"/>
                        <Setter Property="FontSize" Value="13"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                        <Setter Property="MinWidth" Value="130"/>
                        <Setter Property="MinHeight" Value="44"/>
                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                        <Setter Property="VerticalContentAlignment" Value="Center"/>
                        <Setter Property="UseLayoutRounding" Value="True"/>
                        <Setter Property="SnapsToDevicePixels" Value="True"/>
                    </Style>

                    <!-- 自定义卡片样式 -->
                    <Style x:Key="LauncherCardStyle" TargetType="Border">
                        <Setter Property="Background" Value="{DynamicResource RegionBrush}"/>
                        <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
                        <Setter Property="BorderThickness" Value="1"/>
                        <Setter Property="CornerRadius" Value="6"/>
                        <Setter Property="Margin" Value="8"/>
                        <Setter Property="Padding" Value="20"/>
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                            </Setter.Value>
                        </Setter>
                    </Style>

                    <!-- 配置项卡片样式 -->
                    <Style x:Key="ConfigItemCardStyle" TargetType="Border">
                        <Setter Property="Background" Value="{DynamicResource RegionBrush}"/>
                        <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
                        <Setter Property="BorderThickness" Value="2"/>
                        <Setter Property="CornerRadius" Value="6"/>
                        <Setter Property="Margin" Value="2"/>
                        <Setter Property="Padding" Value="12"/>
                        <Setter Property="Effect">
                            <Setter.Value>
                                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="1" BlurRadius="4"/>
                            </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                            <!-- 鼠标悬停效果 -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="BorderBrush" Value="{StaticResource HoverBorderBrush}"/>
                                <Setter Property="Background" Value="{StaticResource HoverBackgroundBrush}"/>
                                <Setter Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="Black" Opacity="0.15" ShadowDepth="2" BlurRadius="6"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
