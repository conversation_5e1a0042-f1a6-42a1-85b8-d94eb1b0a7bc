using Chat.Models;
using Chat.Services;
using System;
using System.Windows;
using System.Windows.Controls;

namespace Chat.Views
{
    public partial class FingerprintTestWindow : Window
    {
        private readonly BrowserFingerprint _fingerprint;
        private readonly IBrowserFingerprintService _fingerprintService;

        public FingerprintTestWindow(BrowserFingerprint fingerprint)
        {
            InitializeComponent();
            _fingerprint = fingerprint;
            _fingerprintService = new BrowserFingerprintService();
            
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            FingerprintNameText.Text = $"测试指纹: {_fingerprint.Name}";
            DisplayFingerprintInfo();
            LogMessage("指纹测试窗口已初始化");
        }

        private void DisplayFingerprintInfo()
        {
            FingerprintInfoPanel.Children.Clear();

            // 基本信息
            AddInfoItem("指纹名称", _fingerprint.Name);
            AddInfoItem("User Agent", _fingerprint.UserAgent);
            AddInfoItem("语言", _fingerprint.Language);
            AddInfoItem("平台", _fingerprint.Platform);
            AddInfoItem("时区", _fingerprint.TimeZone);
            
            // 屏幕信息
            AddInfoItem("屏幕分辨率", $"{_fingerprint.Screen.Width}x{_fingerprint.Screen.Height}");
            AddInfoItem("颜色深度", $"{_fingerprint.Screen.ColorDepth}位");
            AddInfoItem("像素比", _fingerprint.Screen.DevicePixelRatio.ToString("F1"));
            
            // 硬件信息
            AddInfoItem("CPU核心数", _fingerprint.Hardware.CpuCores.ToString());
            AddInfoItem("内存", $"{_fingerprint.Hardware.Memory}GB");
            AddInfoItem("设备名称", _fingerprint.Hardware.DeviceName);
            
            // Canvas和音频
            AddInfoItem("Canvas噪声", _fingerprint.Canvas.NoiseLevel);
            AddInfoItem("音频噪声", _fingerprint.Audio.NoiseLevel);
            
            // WebRTC
            AddInfoItem("WebRTC", _fingerprint.WebRTC.Enabled ? "启用" : "禁用");
            AddInfoItem("阻止本地IP", _fingerprint.WebRTC.BlockLocalIP ? "是" : "否");
        }

        private void AddInfoItem(string label, string value)
        {
            var panel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 2, 0, 2) };
            
            var labelBlock = new TextBlock 
            { 
                Text = $"{label}:", 
                Width = 100, 
                FontWeight = FontWeights.Bold,
                VerticalAlignment = VerticalAlignment.Top
            };
            
            var valueBlock = new TextBlock 
            { 
                Text = value, 
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(5, 0, 0, 0)
            };
            
            panel.Children.Add(labelBlock);
            panel.Children.Add(valueBlock);
            FingerprintInfoPanel.Children.Add(panel);
        }

        private void LogMessage(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            LogTextBlock.Text += $"[{timestamp}] {message}\n";
            
            // 自动滚动到底部
            LogScrollViewer.ScrollToEnd();
        }

        private async void ApplyFingerprintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LogMessage("正在应用指纹配置...");

                // 检查浏览器状态
                if (!TestBrowser.IsBrowserInitialized)
                {
                    LogMessage("⚠️ 浏览器尚未初始化，等待初始化完成...");
                    await Task.Delay(2000);
                }

                var success = await _fingerprintService.ApplyFingerprintToBrowserAsync(_fingerprint, TestBrowser);

                if (success)
                {
                    LogMessage("✓ 指纹配置应用成功");
                }
                else
                {
                    LogMessage("✗ 指纹配置应用失败");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"✗ 应用指纹时出错: {ex.Message}");
            }
        }

        private async void TestFingerprintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LogMessage("开始执行指纹测试...");
                
                // 执行一系列测试脚本
                await RunFingerprintTests();
            }
            catch (Exception ex)
            {
                LogMessage($"✗ 指纹测试出错: {ex.Message}");
            }
        }

        private async System.Threading.Tasks.Task RunFingerprintTests()
        {
            try
            {
                // 检查浏览器状态
                if (!TestBrowser.IsBrowserInitialized)
                {
                    LogMessage("⚠️ 浏览器尚未初始化，无法执行测试");
                    return;
                }

                var mainFrame = TestBrowser.GetMainFrame();
                if (mainFrame == null)
                {
                    LogMessage("⚠️ 无法获取主框架，无法执行测试");
                    return;
                }

                // 测试User Agent
                var userAgentScript = "navigator.userAgent";
                var userAgentResult = await mainFrame.EvaluateScriptAsync(userAgentScript);
                if (userAgentResult.Success)
                {
                    LogMessage($"User Agent: {userAgentResult.Result}");
                }

                // 测试屏幕信息
                var screenScript = @"
                    JSON.stringify({
                        width: screen.width,
                        height: screen.height,
                        colorDepth: screen.colorDepth,
                        pixelDepth: screen.pixelDepth,
                        devicePixelRatio: window.devicePixelRatio
                    })
                ";
                var screenResult = await mainFrame.EvaluateScriptAsync(screenScript);
                if (screenResult.Success)
                {
                    LogMessage($"屏幕信息: {screenResult.Result}");
                }

                // 测试硬件信息
                var hardwareScript = @"
                    JSON.stringify({
                        hardwareConcurrency: navigator.hardwareConcurrency,
                        deviceMemory: navigator.deviceMemory || 'N/A',
                        platform: navigator.platform,
                        language: navigator.language,
                        languages: navigator.languages
                    })
                ";
                var hardwareResult = await mainFrame.EvaluateScriptAsync(hardwareScript);
                if (hardwareResult.Success)
                {
                    LogMessage($"硬件信息: {hardwareResult.Result}");
                }

                // 测试时区
                var timezoneScript = @"
                    JSON.stringify({
                        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                        timezoneOffset: new Date().getTimezoneOffset()
                    })
                ";
                var timezoneResult = await mainFrame.EvaluateScriptAsync(timezoneScript);
                if (timezoneResult.Success)
                {
                    LogMessage($"时区信息: {timezoneResult.Result}");
                }

                // 测试Canvas指纹
                var canvasScript = @"
                    (function() {
                        try {
                            var canvas = document.createElement('canvas');
                            var ctx = canvas.getContext('2d');
                            ctx.textBaseline = 'top';
                            ctx.font = '14px Arial';
                            ctx.fillText('Canvas fingerprint test 🎨', 2, 2);
                            return canvas.toDataURL().substring(0, 50) + '...';
                        } catch(e) {
                            return 'Canvas test failed: ' + e.message;
                        }
                    })()
                ";
                var canvasResult = await mainFrame.EvaluateScriptAsync(canvasScript);
                if (canvasResult.Success)
                {
                    LogMessage($"Canvas指纹: {canvasResult.Result}");
                }

                // 测试WebGL信息
                var webglScript = @"
                    (function() {
                        try {
                            var canvas = document.createElement('canvas');
                            var gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                            if (!gl) return 'WebGL not supported';

                            var debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                            return JSON.stringify({
                                vendor: gl.getParameter(gl.VENDOR),
                                renderer: gl.getParameter(gl.RENDERER),
                                version: gl.getParameter(gl.VERSION),
                                unmaskedVendor: debugInfo ? gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) : 'N/A',
                                unmaskedRenderer: debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : 'N/A'
                            });
                        } catch(e) {
                            return 'WebGL test failed: ' + e.message;
                        }
                    })()
                ";
                var webglResult = await mainFrame.EvaluateScriptAsync(webglScript);
                if (webglResult.Success)
                {
                    LogMessage($"WebGL信息: {webglResult.Result}");
                }

                LogMessage("✓ 指纹测试完成");
            }
            catch (Exception ex)
            {
                LogMessage($"✗ 测试过程中出错: {ex.Message}");
            }
        }

        private void LoadTestSiteButton_Click(object sender, RoutedEventArgs e)
        {
            if (TestSiteComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var url = selectedItem.Tag.ToString();
                LogMessage($"正在加载测试网站: {url}");
                TestBrowser.LoadUrl(url);
            }
        }
    }
}
