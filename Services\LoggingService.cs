using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;

namespace Chat.Services
{
    /// <summary>
    /// 日志服务类 - 提供统一的日志记录接口
    /// </summary>
    public static class LoggingService
    {
        // 不同类型的日志记录器
        private static readonly NLog.Logger GeneralLogger = NLog.LogManager.GetCurrentClassLogger();
        private static readonly NLog.Logger HttpLogger = NLog.LogManager.GetLogger("Chat.Http.Requests");
        private static readonly NLog.Logger TcpLogger = NLog.LogManager.GetLogger("Chat.Tcp.Connections");
        private static readonly NLog.Logger FileOpsLogger = NLog.LogManager.GetLogger("Chat.FileOps.Operations");
        private static readonly NLog.Logger DatabaseLogger = NLog.LogManager.GetLogger("Chat.Database.Operations");
        private static readonly NLog.Logger ProxyLogger = NLog.LogManager.GetLogger("Chat.Proxy.Operations");
        private static readonly NLog.Logger ChatLogger = NLog.LogManager.GetLogger("Chat.ChatExtraction.Operations");

        /// <summary>
        /// 初始化日志服务
        /// </summary>
        public static void Initialize()
        {
            try
            {
                // 确保日志目录存在
                var logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }

                var archiveDirectory = Path.Combine(logDirectory, "archives");
                if (!Directory.Exists(archiveDirectory))
                {
                    Directory.CreateDirectory(archiveDirectory);
                }

                LogInfo("日志服务初始化完成");
            }
            catch (Exception ex)
            {
                // 如果日志初始化失败，至少输出到控制台
                Console.WriteLine($"日志服务初始化失败: {ex.Message}");
            }
        }

        #region 通用日志方法

        /// <summary>
        /// 记录信息日志
        /// </summary>
        public static void LogInfo(string message, params object[] args)
        {
            GeneralLogger.Info(message, args);
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        public static void LogDebug(string message, params object[] args)
        {
            GeneralLogger.Debug(message, args);
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        public static void LogWarning(string message, params object[] args)
        {
            GeneralLogger.Warn(message, args);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        public static void LogError(string message, Exception exception = null, params object[] args)
        {
            if (exception != null)
            {
                GeneralLogger.Error(exception, message, args);
            }
            else
            {
                GeneralLogger.Error(message, args);
            }
        }

        /// <summary>
        /// 记录致命错误日志
        /// </summary>
        public static void LogFatal(string message, Exception exception = null, params object[] args)
        {
            if (exception != null)
            {
                GeneralLogger.Fatal(exception, message, args);
            }
            else
            {
                GeneralLogger.Fatal(message, args);
            }
        }

        #endregion

        #region HTTP请求日志

        /// <summary>
        /// 记录HTTP请求开始
        /// </summary>
        public static void LogHttpRequestStart(string method, string url, string headers = null)
        {
            var message = $"HTTP请求开始: {method} {url}";
            if (!string.IsNullOrEmpty(headers))
            {
                message += $" | Headers: {headers}";
            }
            HttpLogger.Info(message);
        }

        /// <summary>
        /// 记录HTTP请求完成
        /// </summary>
        public static void LogHttpRequestComplete(string method, string url, int statusCode, long elapsedMs, string responseSize = null)
        {
            var message = $"HTTP请求完成: {method} {url} | 状态码: {statusCode} | 耗时: {elapsedMs}ms";
            if (!string.IsNullOrEmpty(responseSize))
            {
                message += $" | 响应大小: {responseSize}";
            }
            HttpLogger.Info(message);
        }

        /// <summary>
        /// 记录HTTP请求错误
        /// </summary>
        public static void LogHttpRequestError(string method, string url, Exception exception, long elapsedMs = 0)
        {
            var message = $"HTTP请求失败: {method} {url} | 耗时: {elapsedMs}ms | 错误: {exception.Message}";
            HttpLogger.Error(exception, message);
        }

        #endregion

        #region TCP连接日志

        /// <summary>
        /// 记录TCP连接建立
        /// </summary>
        public static void LogTcpConnectionStart(string host, int port, string connectionType = "TCP")
        {
            TcpLogger.Info($"TCP连接开始: {connectionType} {host}:{port}");
        }

        /// <summary>
        /// 记录TCP连接成功
        /// </summary>
        public static void LogTcpConnectionSuccess(string host, int port, long elapsedMs, string connectionType = "TCP")
        {
            TcpLogger.Info($"TCP连接成功: {connectionType} {host}:{port} | 耗时: {elapsedMs}ms");
        }

        /// <summary>
        /// 记录TCP连接失败
        /// </summary>
        public static void LogTcpConnectionError(string host, int port, Exception exception, string connectionType = "TCP")
        {
            TcpLogger.Error(exception, $"TCP连接失败: {connectionType} {host}:{port} | 错误: {exception.Message}");
        }

        /// <summary>
        /// 记录TCP数据传输
        /// </summary>
        public static void LogTcpDataTransfer(string host, int port, string direction, int bytes, string dataType = "数据")
        {
            TcpLogger.Debug($"TCP数据传输: {host}:{port} | {direction} | {dataType}: {bytes} bytes");
        }

        #endregion

        #region 文件操作日志

        /// <summary>
        /// 记录文件读取操作
        /// </summary>
        public static void LogFileRead(string filePath, long fileSize = 0, long elapsedMs = 0)
        {
            var message = $"文件读取: {filePath}";
            if (fileSize > 0) message += $" | 大小: {fileSize} bytes";
            if (elapsedMs > 0) message += $" | 耗时: {elapsedMs}ms";
            FileOpsLogger.Info(message);
        }

        /// <summary>
        /// 记录文件写入操作
        /// </summary>
        public static void LogFileWrite(string filePath, long fileSize = 0, long elapsedMs = 0)
        {
            var message = $"文件写入: {filePath}";
            if (fileSize > 0) message += $" | 大小: {fileSize} bytes";
            if (elapsedMs > 0) message += $" | 耗时: {elapsedMs}ms";
            FileOpsLogger.Info(message);
        }

        /// <summary>
        /// 记录文件操作错误
        /// </summary>
        public static void LogFileError(string operation, string filePath, Exception exception)
        {
            FileOpsLogger.Error(exception, $"文件操作失败: {operation} {filePath} | 错误: {exception.Message}");
        }

        /// <summary>
        /// 记录文件删除操作
        /// </summary>
        public static void LogFileDelete(string filePath)
        {
            FileOpsLogger.Info($"文件删除: {filePath}");
        }

        #endregion

        #region 数据库操作日志

        /// <summary>
        /// 记录数据库查询
        /// </summary>
        public static void LogDatabaseQuery(string query, long elapsedMs = 0, int recordCount = 0)
        {
            var message = $"数据库查询: {query}";
            if (elapsedMs > 0) message += $" | 耗时: {elapsedMs}ms";
            if (recordCount > 0) message += $" | 记录数: {recordCount}";
            DatabaseLogger.Info(message);
        }

        /// <summary>
        /// 记录数据库更新
        /// </summary>
        public static void LogDatabaseUpdate(string operation, string table, int affectedRows = 0, long elapsedMs = 0)
        {
            var message = $"数据库更新: {operation} {table}";
            if (affectedRows > 0) message += $" | 影响行数: {affectedRows}";
            if (elapsedMs > 0) message += $" | 耗时: {elapsedMs}ms";
            DatabaseLogger.Info(message);
        }

        /// <summary>
        /// 记录数据库错误
        /// </summary>
        public static void LogDatabaseError(string operation, Exception exception, string additionalInfo = null)
        {
            var message = $"数据库操作失败: {operation} | 错误: {exception.Message}";
            if (!string.IsNullOrEmpty(additionalInfo)) message += $" | 附加信息: {additionalInfo}";
            DatabaseLogger.Error(exception, message);
        }

        #endregion

        #region 代理操作日志

        /// <summary>
        /// 记录代理配置
        /// </summary>
        public static void LogProxyConfig(string proxyType, string host, int port, bool hasAuth = false)
        {
            var message = $"代理配置: {proxyType} {host}:{port}";
            if (hasAuth) message += " | 包含认证信息";
            ProxyLogger.Info(message);
        }

        /// <summary>
        /// 记录代理连接测试
        /// </summary>
        public static void LogProxyTest(string proxyUrl, bool success, long elapsedMs = 0, string errorMessage = null)
        {
            var message = $"代理测试: {proxyUrl} | 结果: {(success ? "成功" : "失败")}";
            if (elapsedMs > 0) message += $" | 耗时: {elapsedMs}ms";
            if (!string.IsNullOrEmpty(errorMessage)) message += $" | 错误: {errorMessage}";
            
            if (success)
                ProxyLogger.Info(message);
            else
                ProxyLogger.Error(message);
        }

        /// <summary>
        /// 记录代理认证
        /// </summary>
        public static void LogProxyAuth(string proxyUrl, bool success, string errorMessage = null)
        {
            var message = $"代理认证: {proxyUrl} | 结果: {(success ? "成功" : "失败")}";
            if (!string.IsNullOrEmpty(errorMessage)) message += $" | 错误: {errorMessage}";
            
            if (success)
                ProxyLogger.Info(message);
            else
                ProxyLogger.Error(message);
        }

        #endregion

        #region 聊天提取日志

        /// <summary>
        /// 记录聊天提取开始
        /// </summary>
        public static void LogChatExtractionStart(string platform, string url)
        {
            ChatLogger.Info($"聊天提取开始: {platform} | URL: {url}");
        }

        /// <summary>
        /// 记录聊天消息提取
        /// </summary>
        public static void LogChatMessagesExtracted(string platform, int messageCount, long elapsedMs = 0)
        {
            var message = $"聊天消息提取: {platform} | 消息数: {messageCount}";
            if (elapsedMs > 0) message += $" | 耗时: {elapsedMs}ms";
            ChatLogger.Info(message);
        }

        /// <summary>
        /// 记录聊天提取错误
        /// </summary>
        public static void LogChatExtractionError(string platform, Exception exception, string additionalInfo = null)
        {
            var message = $"聊天提取失败: {platform} | 错误: {exception.Message}";
            if (!string.IsNullOrEmpty(additionalInfo)) message += $" | 附加信息: {additionalInfo}";
            ChatLogger.Error(exception, message);
        }

        /// <summary>
        /// 记录JavaScript执行
        /// </summary>
        public static void LogJavaScriptExecution(string script, bool success, string result = null, long elapsedMs = 0)
        {
            var message = $"JavaScript执行: {(success ? "成功" : "失败")} | 脚本长度: {script.Length}字符";
            if (elapsedMs > 0) message += $" | 耗时: {elapsedMs}ms";
            if (!string.IsNullOrEmpty(result)) message += $" | 结果: {result}";
            
            if (success)
                ChatLogger.Debug(message);
            else
                ChatLogger.Error(message);
        }

        #endregion

        /// <summary>
        /// 关闭日志服务
        /// </summary>
        public static void Shutdown()
        {
            try
            {
                LogInfo("日志服务正在关闭...");
                NLog.LogManager.Shutdown();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"日志服务关闭失败: {ex.Message}");
            }
        }
    }
}
