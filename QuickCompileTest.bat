@echo off
echo ========================================
echo Quick Compile Test
echo ========================================
echo.

echo Current directory: %CD%
echo.

echo Checking if files exist...
if exist "ChatLauncher\ChatLauncher.csproj" (
    echo ✓ ChatLauncher.csproj found
) else (
    echo ✗ ChatLauncher.csproj not found
    pause
    exit /b 1
)

if exist "ChatLauncher\ViewModels\MainViewModel.cs" (
    echo ✓ MainViewModel.cs found
) else (
    echo ✗ MainViewModel.cs not found
    pause
    exit /b 1
)

if exist "Chat.csproj" (
    echo ✓ Chat.csproj found
) else (
    echo ✗ Chat.csproj not found
    pause
    exit /b 1
)

echo.
echo All required files found!
echo.

echo Testing ChatLauncher compilation...
echo Command: msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU
echo.

msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ ChatLauncher compiled successfully!
    
    if exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
        echo ✓ ChatLauncher.exe created
        echo.
        echo You can now run: ChatLauncher\bin\Debug\ChatLauncher.exe
    ) else (
        echo ✗ ChatLauncher.exe not found in output directory
    )
) else (
    echo.
    echo ✗ ChatLauncher compilation failed!
    echo Please check the error messages above.
)

echo.
pause
