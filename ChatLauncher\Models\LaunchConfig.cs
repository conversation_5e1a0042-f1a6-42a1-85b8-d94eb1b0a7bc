using System;
using System.Collections.Generic;

namespace ChatLauncher.Models
{
    /// <summary>
    /// 启动配置模型
    /// </summary>
    public class LaunchConfig
    {
        /// <summary>
        /// 配置名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 启动URL
        /// </summary>
        public string Url { get; set; }

        /// <summary>
        /// 代理设置
        /// </summary>
        public string Proxy { get; set; }

        /// <summary>
        /// 是否启用调试模式
        /// </summary>
        public bool Debug { get; set; }

        /// <summary>
        /// 用户数据目录
        /// </summary>
        public string UserDataDir { get; set; }

        /// <summary>
        /// 缓存目录
        /// </summary>
        public string CacheDir { get; set; }

        /// <summary>
        /// 窗口宽度
        /// </summary>
        public int? Width { get; set; }

        /// <summary>
        /// 窗口高度
        /// </summary>
        public int? Height { get; set; }

        /// <summary>
        /// 是否全屏
        /// </summary>
        public bool Fullscreen { get; set; }

        /// <summary>
        /// 额外的命令行参数
        /// </summary>
        public string ExtraArgs { get; set; }

        /// <summary>
        /// 配置描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 配置图标（可选）
        /// </summary>
        public string Icon { get; set; }

        /// <summary>
        /// 是否为收藏配置
        /// </summary>
        public bool IsFavorite { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后使用时间
        /// </summary>
        public DateTime LastUsedTime { get; set; }

        /// <summary>
        /// 使用次数
        /// </summary>
        public int UseCount { get; set; }

        /// <summary>
        /// 生成命令行参数
        /// </summary>
        public string GenerateCommandLineArgs()
        {
            var args = new List<string>();

            if (!string.IsNullOrEmpty(Url))
            {
                args.Add($"--url \"{Url}\"");
            }

            if (!string.IsNullOrEmpty(Proxy))
            {
                args.Add($"--proxy \"{Proxy}\"");
            }

            if (Debug)
            {
                args.Add("--debug");
            }

            if (!string.IsNullOrEmpty(UserDataDir))
            {
                args.Add($"--user-data-dir \"{UserDataDir}\"");
            }

            if (!string.IsNullOrEmpty(CacheDir))
            {
                args.Add($"--cache-dir \"{CacheDir}\"");
            }

            if (Width.HasValue && Height.HasValue)
            {
                args.Add($"--window-size {Width}x{Height}");
            }

            if (Fullscreen)
            {
                args.Add("--fullscreen");
            }

            if (!string.IsNullOrEmpty(ExtraArgs))
            {
                args.Add(ExtraArgs);
            }

            return string.Join(" ", args);
        }

        /// <summary>
        /// 克隆配置
        /// </summary>
        public LaunchConfig Clone()
        {
            return new LaunchConfig
            {
                Name = this.Name + " (副本)",
                Url = this.Url,
                Proxy = this.Proxy,
                Debug = this.Debug,
                UserDataDir = this.UserDataDir,
                CacheDir = this.CacheDir,
                Width = this.Width,
                Height = this.Height,
                Fullscreen = this.Fullscreen,
                ExtraArgs = this.ExtraArgs,
                Description = this.Description,
                Icon = this.Icon,
                IsFavorite = false,
                CreatedTime = DateTime.Now,
                UseCount = 0
            };
        }

        /// <summary>
        /// 验证配置
        /// </summary>
        public bool IsValid(out string errorMessage)
        {
            errorMessage = null;

            if (string.IsNullOrWhiteSpace(Name))
            {
                errorMessage = "配置名称不能为空";
                return false;
            }

            if (!string.IsNullOrEmpty(Url))
            {
                if (!Uri.TryCreate(Url, UriKind.Absolute, out _))
                {
                    errorMessage = "URL格式不正确";
                    return false;
                }
            }

            if (!string.IsNullOrEmpty(Proxy))
            {
                // 简单的代理格式验证
                if (!Proxy.Contains("://"))
                {
                    errorMessage = "代理格式不正确，应包含协议（如 http://、socks5://）";
                    return false;
                }
            }

            return true;
        }

        public override string ToString()
        {
            return Name;
        }
    }

    /// <summary>
    /// 预定义的启动配置
    /// </summary>
    public static class PredefinedConfigs
    {
        public static List<LaunchConfig> GetDefaultConfigs()
        {
            return new List<LaunchConfig>
            {
                new LaunchConfig
                {
                    Name = "默认启动",
                    Description = "不带任何参数的默认启动",
                    Debug = true,
                    Icon = "🚀"
                },
                new LaunchConfig
                {
                    Name = "WhatsApp Web",
                    Url = "https://web.whatsapp.com",
                    Description = "启动并访问WhatsApp Web",
                    Debug = true,
                    Icon = "💬"
                },
                new LaunchConfig
                {
                    Name = "Telegram Web",
                    Url = "https://web.telegram.org",
                    Description = "启动并访问Telegram Web",
                    Debug = true,
                    Icon = "✈️"
                },
                new LaunchConfig
                {
                    Name = "Facebook Messenger",
                    Url = "https://www.messenger.com",
                    Description = "启动并访问Facebook Messenger",
                    Debug = true,
                    Icon = "📘"
                },
                new LaunchConfig
                {
                    Name = "Discord Web",
                    Url = "https://discord.com/app",
                    Description = "启动并访问Discord Web",
                    Debug = true,
                    Icon = "🎮"
                },
                new LaunchConfig
                {
                    Name = "浏览器指纹测试",
                    Url = "https://www.browserscan.net/zh",
                    Description = "测试浏览器指纹和代理设置",
                    Debug = true,
                    Icon = "🔍"
                },
                new LaunchConfig
                {
                    Name = "代理测试 (HTTP)",
                    Url = "https://www.browserscan.net/zh",
                    Proxy = "http://127.0.0.1:8080",
                    Description = "使用HTTP代理测试",
                    Debug = true,
                    Icon = "🌐"
                },
                new LaunchConfig
                {
                    Name = "代理测试 (SOCKS5)",
                    Url = "https://www.browserscan.net/zh",
                    Proxy = "socks5://127.0.0.1:1080",
                    Description = "使用SOCKS5代理测试",
                    Debug = true,
                    Icon = "🔒"
                }
            };
        }
    }
}
