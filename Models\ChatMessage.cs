using System;

namespace Chat.Models
{
    public class ChatMessage
    {
        public int Id { get; set; }
        public string Content { get; set; }
        public string Sender { get; set; }
        public DateTime Timestamp { get; set; }
        public MessageType Type { get; set; }
        public string Platform { get; set; }

        public ChatMessage()
        {
            Timestamp = DateTime.Now;
            Type = MessageType.Text;
        }

        public ChatMessage(string content, string sender) : this()
        {
            Content = content;
            Sender = sender;
        }
    }

    public enum MessageType
    {
        Text,
        Image,
        File,
        System
    }
}
