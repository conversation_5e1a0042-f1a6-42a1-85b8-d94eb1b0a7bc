﻿<Window x:Class="Chat.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:wpf="clr-namespace:CefSharp.Wpf;assembly=CefSharp.Wpf"
        xmlns:local="clr-namespace:Chat"
        xmlns:prism="http://prismlibrary.com/"
        prism:ViewModelLocator.AutoWireViewModel="True"
        mc:Ignorable="d"
        Title="{Binding Title}" Height="450" Width="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- URL输入栏 -->
        <DockPanel Grid.Row="0" Margin="5">
            <Button DockPanel.Dock="Right" Content="Go" Margin="5,0,0,0"
                    Command="{Binding LoadUrlCommand}"
                    CommandParameter="{Binding ElementName=UrlTextBox, Path=Text}"/>
            <TextBox x:Name="UrlTextBox" Text="{Binding BrowserUrl, UpdateSourceTrigger=PropertyChanged}"
                     VerticalAlignment="Center"/>
        </DockPanel>

        <!-- 浏览器控件 -->
        <wpf:ChromiumWebBrowser x:Name="Browser" Grid.Row="1"
                                Address="{Binding BrowserUrl}"/>
    </Grid>
</Window>
