<hc:Window x:Class="Chat.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:hc="https://handyorg.github.io/handycontrol"
        xmlns:wpf="clr-namespace:CefSharp.Wpf;assembly=CefSharp.Wpf"
        xmlns:local="clr-namespace:Chat"
        Title="聊天应用 - Chat" Height="720" Width="1200"
        MinHeight="500" MinWidth="800"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        mc:Ignorable="d">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <Border Grid.Row="0" Style="{StaticResource ChatCardStyle}" Margin="8,8,8,4">
            <DockPanel>
                <!-- 功能按钮 -->
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                    <Button Content="指纹管理" Style="{StaticResource ChatButtonStyle}"
                            Click="FingerprintManagerButton_Click"
                            Background="{StaticResource DarkPrimaryBrush}" Foreground="White"/>
                    <Button Content="测试提取" Style="{StaticResource ChatButtonStyle}"
                            Click="TestExtractionButton_Click"
                            Background="#E74C3C" Foreground="White"/>
                    <Button Content="多平台聊天" Style="{StaticResource ChatButtonStyle}"
                            Click="MultiChatButton_Click"
                            Background="{StaticResource PrimaryBrush}" Foreground="White"/>
                    <Button Content="Go" Style="{StaticResource ChatButtonStyle}"
                            Click="GoButton_Click"/>
                </StackPanel>

                <!-- URL输入框 -->
                <hc:TextBox x:Name="UrlTextBox"
                           Text="https://www.browserscan.net/zh"
                           hc:InfoElement.Placeholder="请输入网址..."
                           hc:InfoElement.Title="网址"
                           VerticalAlignment="Center"
                           Margin="0,0,12,0"/>
            </DockPanel>
        </Border>

        <!-- 浏览器区域 -->
        <Border Grid.Row="1" Style="{StaticResource ChatCardStyle}" Margin="8,4,8,8">
            <wpf:ChromiumWebBrowser x:Name="Browser"/>
        </Border>
    </Grid>
</Window>
