﻿<Window x:Class="Chat.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:hc="https://handyorg.github.io/handycontrol"
        xmlns:wpf="clr-namespace:CefSharp.Wpf;assembly=CefSharp.Wpf"
        xmlns:local="clr-namespace:Chat"
        Title="聊天应用 - Chat" Height="720" Width="1200"
        MinHeight="500" MinWidth="800"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        mc:Ignorable="d">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <Border Grid.Row="0" Background="LightGray"
                BorderBrush="Gray" BorderThickness="1"
                CornerRadius="6" Margin="8,8,8,4" Padding="16">
            <DockPanel>
                <!-- 功能按钮 -->
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                    <Button Content="指纹管理" Margin="8" Padding="20,12" MinWidth="100" MinHeight="36"
                            Click="FingerprintManagerButton_Click"
                            Background="#9B59B6" Foreground="White"/>
                    <Button Content="测试提取" Margin="8" Padding="20,12" MinWidth="100" MinHeight="36"
                            Click="TestExtractionButton_Click"
                            Background="#E74C3C" Foreground="White"/>
                    <Button Content="多平台聊天" Margin="8" Padding="20,12" MinWidth="100" MinHeight="36"
                            Click="MultiChatButton_Click"
                            Background="#3498DB" Foreground="White"/>
                    <Button Content="Go" Margin="8" Padding="20,12" MinWidth="100" MinHeight="36"
                            Click="GoButton_Click"/>
                </StackPanel>

                <!-- URL输入框 -->
                <TextBox x:Name="UrlTextBox"
                           Text="https://www.browserscan.net/zh"
                           VerticalAlignment="Center"
                           Margin="0,0,12,0"/>
            </DockPanel>
        </Border>

        <!-- 浏览器区域 -->
        <Border Grid.Row="1" Background="White"
                BorderBrush="Gray" BorderThickness="1"
                CornerRadius="6" Margin="8,4,8,8" Padding="16">
            <wpf:ChromiumWebBrowser x:Name="Browser"/>
        </Border>
    </Grid>
</Window>
