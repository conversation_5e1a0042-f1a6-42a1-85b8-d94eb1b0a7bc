﻿<Window x:Class="Chat.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:wpf="clr-namespace:CefSharp.Wpf;assembly=CefSharp.Wpf"
        xmlns:local="clr-namespace:Chat"
        mc:Ignorable="d"
        Title="Chat Application" WindowStartupLocation="CenterScreen" WindowState="Maximized" MinHeight="450" MinWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- URL输入栏 -->
        <DockPanel Grid.Row="0" Margin="5">
            <Button DockPanel.Dock="Right" Content="指纹管理" Margin="5,0,0,0"
                    Click="FingerprintManagerButton_Click" Background="#9B59B6" Foreground="White"/>
            <Button DockPanel.Dock="Right" Content="测试提取" Margin="5,0,5,0"
                    Click="TestExtractionButton_Click" Background="#E74C3C" Foreground="White"/>
            <Button DockPanel.Dock="Right" Content="多平台聊天" Margin="5,0,5,0"
                    Click="MultiChatButton_Click" Background="#3498DB" Foreground="White"/>
            <Button DockPanel.Dock="Right" Content="Go" Margin="5,0,5,0" Click="GoButton_Click"/>
            <TextBox x:Name="UrlTextBox" Text="https://www.psvmc.cn"
                     VerticalAlignment="Center"/>
        </DockPanel>

        <!-- 浏览器控件 -->
        <wpf:ChromiumWebBrowser x:Name="Browser" Grid.Row="1"/>
    </Grid>
</Window>
