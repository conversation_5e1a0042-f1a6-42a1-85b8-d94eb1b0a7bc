@echo off
echo ========================================
echo 聊天集成应用 - 代理功能快速测试
echo ========================================
echo.

echo 测试1: 显示帮助信息
echo ----------------------------------------
Chat.exe --help
echo.
pause

echo 测试2: 无代理启动（调试模式）
echo ----------------------------------------
Chat.exe --debug
echo.
pause

echo 测试3: HTTP代理测试（本地8080端口）
echo ----------------------------------------
echo 注意：请确保本地8080端口有HTTP代理服务运行
Chat.exe --proxy http://127.0.0.1:8080 --debug
echo.
pause

echo 测试4: SOCKS5代理测试（本地1080端口）
echo ----------------------------------------
echo 注意：请确保本地1080端口有SOCKS5代理服务运行
Chat.exe --proxy socks5://127.0.0.1:1080 --debug
echo.
pause

echo 测试5: 代理 + WhatsApp Web
echo ----------------------------------------
Chat.exe --proxy http://127.0.0.1:8080 --url https://web.whatsapp.com --debug
echo.
pause

echo 测试完成！
echo.
echo 如果需要测试带认证的代理，请使用以下格式：
echo Chat.exe --proxy http://username:<EMAIL>:8080
echo Chat.exe --proxy socks5://user:<EMAIL>:1080
echo.
pause
