﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="CefSharp.Common" version="136.1.40" targetFramework="net481" />
  <package id="CefSharp.Wpf" version="136.1.40" targetFramework="net481" />
  <package id="chromiumembeddedframework.runtime.win-x64" version="136.1.4" targetFramework="net481" />
  <package id="chromiumembeddedframework.runtime.win-x86" version="136.1.4" targetFramework="net481" />
  <package id="HandyControl" version="3.5.1" targetFramework="net481" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net481" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="8.0.1" targetFramework="net481" />
  <package id="Microsoft.Xaml.Behaviors.Wpf" version="1.1.122" targetFramework="net481" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net481" />
  <package id="Prism.Container.Abstractions" version="9.0.106" targetFramework="net481" />
  <package id="Prism.Container.Unity" version="9.0.106" targetFramework="net481" />
  <package id="Prism.Core" version="9.0.537" targetFramework="net481" />
  <package id="Prism.Events" version="9.0.537" targetFramework="net481" />
  <package id="Prism.Unity" version="9.0.537" targetFramework="net481" />
  <package id="Prism.Wpf" version="9.0.537" targetFramework="net481" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net481" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net481" />
  <package id="Unity.Abstractions" version="5.11.7" targetFramework="net481" />
  <package id="Unity.Container" version="5.11.11" targetFramework="net481" />
</packages>