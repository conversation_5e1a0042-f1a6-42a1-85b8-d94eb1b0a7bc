<hc:Window x:Class="ChatLauncher.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:hc="https://handyorg.github.io/handycontrol"
        Title="聊天应用启动器" Height="700" Width="1100"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize">
    <hc:Window.Resources>
        <!-- Converters -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- 配置项数据模板 -->
        <DataTemplate x:Key="ConfigItemTemplate">
            <Border Style="{StaticResource LauncherCardStyle}" Margin="2" Padding="12">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 图标 -->
                    <Border Grid.Column="0"
                            Background="{DynamicResource PrimaryBrush}"
                            CornerRadius="20"
                            Width="40" Height="40"
                            Margin="0,0,12,0">
                        <TextBlock Text="{Binding Icon}"
                                 FontSize="18"
                                 Foreground="White"
                                 HorizontalAlignment="Center"
                                 VerticalAlignment="Center"/>
                    </Border>

                    <!-- 内容 -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                        <TextBlock Text="{Binding Name}"
                                 FontWeight="Bold"
                                 FontSize="15"
                                 Foreground="{DynamicResource PrimaryTextBrush}"/>
                        <TextBlock Text="{Binding Description}"
                                 FontSize="12"
                                 Foreground="{DynamicResource SecondaryTextBrush}"
                                 TextWrapping="Wrap"
                                 Margin="0,2,0,0"/>
                    </StackPanel>

                    <!-- 状态和统计 -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                        <Border Background="{DynamicResource PrimaryBrush}"
                              CornerRadius="10"
                              Padding="6,2"
                              Margin="4,0,8,0"
                              Visibility="{Binding UseCount, Converter={x:Static hc:Converters.Int2VisibilityConverter}}">
                            <TextBlock Text="{Binding UseCount}"
                                     Foreground="White"
                                     FontSize="10"
                                     FontWeight="Bold"/>
                        </Border>
                        <TextBlock Text="⭐"
                                 Visibility="{Binding IsFavorite, Converter={StaticResource BooleanToVisibilityConverter}}"
                                 FontSize="14"
                                 Foreground="#FFD4AF37"/>
                    </StackPanel>
                </Grid>
            </Border>
        </DataTemplate>
    </hc:Window.Resources>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Style="{StaticResource LauncherCardStyle}" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <Border Background="{DynamicResource PrimaryBrush}"
                            CornerRadius="25"
                            Width="50" Height="50"
                            Margin="0,0,15,0">
                        <TextBlock Text="🚀"
                                 FontSize="24"
                                 Foreground="White"
                                 HorizontalAlignment="Center"
                                 VerticalAlignment="Center"/>
                    </Border>
                    <StackPanel VerticalAlignment="Center">
                        <TextBlock Text="聊天应用启动器"
                                 FontSize="20"
                                 FontWeight="Bold"
                                 Foreground="{DynamicResource PrimaryTextBrush}"/>
                        <TextBlock x:Name="StatusText"
                                 Text="就绪"
                                 FontSize="13"
                                 Foreground="{DynamicResource SecondaryTextBrush}"
                                 Margin="0,2,0,0"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="RefreshButton"
                            Content="🔄 刷新"
                            Style="{StaticResource LauncherButtonStyle}"
                            Click="RefreshButton_Click"/>
                    <Button x:Name="SettingsButton"
                            Content="⚙️ 设置"
                            Style="{StaticResource LauncherButtonStyle}"
                            Click="SettingsButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="0,10,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="400"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：配置列表 -->
            <Border Grid.Column="0" Style="{StaticResource LauncherCardStyle}" Margin="0,0,5,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 标题 -->
                    <TextBlock Grid.Row="0"
                             Text="启动配置"
                             FontSize="16"
                             FontWeight="Bold"
                             Foreground="{DynamicResource PrimaryTextBrush}"
                             Margin="0,0,0,15"/>

                    <!-- 搜索框 -->
                    <TextBox x:Name="SearchBox"
                           Grid.Row="1"
                           Margin="0,0,0,15"
                           hc:InfoElement.Placeholder="🔍 搜索配置..."
                           TextChanged="SearchBox_TextChanged"/>

                    <!-- 配置列表 -->
                    <ScrollViewer Grid.Row="2"
                                VerticalScrollBarVisibility="Auto"
                                HorizontalScrollBarVisibility="Disabled">
                        <ListBox x:Name="ConfigListBox"
                               Background="Transparent"
                               BorderThickness="0"
                               ItemTemplate="{StaticResource ConfigItemTemplate}"
                               SelectionChanged="ConfigListBox_SelectionChanged">
                            <ListBox.ItemContainerStyle>
                                <Style TargetType="ListBoxItem">
                                    <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                    <Setter Property="Padding" Value="0"/>
                                    <Setter Property="Margin" Value="0,0,0,5"/>
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Setter Property="BorderThickness" Value="0"/>
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="ListBoxItem">
                                                <ContentPresenter/>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </ListBox.ItemContainerStyle>
                        </ListBox>
                    </ScrollViewer>

                    <!-- 配置管理按钮 -->
                    <StackPanel Grid.Row="3"
                              Orientation="Horizontal"
                              HorizontalAlignment="Center"
                              Margin="0,15,0,0">
                        <Button x:Name="AddConfigButton"
                                Content="➕ 新建"
                                Style="{StaticResource ButtonSuccess}"
                                Click="AddConfigButton_Click"/>
                        <Button x:Name="EditConfigButton"
                                Content="✏️ 编辑"
                                Style="{StaticResource ButtonWarning}"
                                Click="EditConfigButton_Click"/>
                        <Button x:Name="DeleteConfigButton"
                                Content="🗑️ 删除"
                                Style="{StaticResource ButtonDanger}"
                                Click="DeleteConfigButton_Click"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 右侧：配置详情和启动选项 -->
            <Border Grid.Column="1" Style="{StaticResource LauncherCardStyle}" Margin="5,0,0,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel x:Name="DetailsPanel">
                        <!-- 配置详情 -->
                        <GroupBox Header="配置详情" Margin="0,0,0,20">
                            <Grid Margin="15">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0"
                                         Text="名称："
                                         FontWeight="Medium"
                                         Foreground="{DynamicResource SecondaryTextBrush}"
                                         VerticalAlignment="Center"
                                         Margin="0,0,0,10"/>
                                <TextBlock x:Name="ConfigNameText"
                                         Grid.Row="0" Grid.Column="1"
                                         FontWeight="Bold"
                                         FontSize="14"
                                         Foreground="{DynamicResource PrimaryTextBrush}"
                                         VerticalAlignment="Center"
                                         Margin="0,0,0,10"/>

                                <TextBlock Grid.Row="1" Grid.Column="0"
                                         Text="URL："
                                         FontWeight="Medium"
                                         Foreground="{DynamicResource SecondaryTextBrush}"
                                         VerticalAlignment="Top"
                                         Margin="0,0,0,10"/>
                                <TextBlock x:Name="ConfigUrlText"
                                         Grid.Row="1" Grid.Column="1"
                                         TextWrapping="Wrap"
                                         Foreground="{DynamicResource PrimaryTextBrush}"
                                         Margin="0,0,0,10"/>

                                <TextBlock Grid.Row="2" Grid.Column="0"
                                         Text="代理："
                                         FontWeight="Medium"
                                         Foreground="{DynamicResource SecondaryTextBrush}"
                                         VerticalAlignment="Center"
                                         Margin="0,0,0,10"/>
                                <TextBlock x:Name="ConfigProxyText"
                                         Grid.Row="2" Grid.Column="1"
                                         Foreground="{DynamicResource PrimaryTextBrush}"
                                         VerticalAlignment="Center"
                                         Margin="0,0,0,10"/>

                                <TextBlock Grid.Row="3" Grid.Column="0"
                                         Text="调试："
                                         FontWeight="Medium"
                                         Foreground="{DynamicResource SecondaryTextBrush}"
                                         VerticalAlignment="Center"
                                         Margin="0,0,0,10"/>
                                <TextBlock x:Name="ConfigDebugText"
                                         Grid.Row="3" Grid.Column="1"
                                         Foreground="{DynamicResource PrimaryTextBrush}"
                                         VerticalAlignment="Center"
                                         Margin="0,0,0,10"/>

                                <TextBlock Grid.Row="4" Grid.Column="0"
                                         Text="使用次数："
                                         FontWeight="Medium"
                                         Foreground="{DynamicResource SecondaryTextBrush}"
                                         VerticalAlignment="Center"/>
                                <TextBlock x:Name="ConfigUseCountText"
                                         Grid.Row="4" Grid.Column="1"
                                         Text="0"
                                         FontWeight="Bold"
                                         Foreground="{DynamicResource PrimaryBrush}"
                                         VerticalAlignment="Center"/>
                            </Grid>
                        </GroupBox>

                        <!-- 启动选项 -->
                        <GroupBox Header="启动选项" Margin="0,0,0,20">
                            <StackPanel Margin="15">
                                <CheckBox x:Name="MultiInstanceCheckBox"
                                        Content="启动多个实例"
                                        FontSize="14"
                                        Margin="0,0,0,15"/>

                                <StackPanel Orientation="Horizontal"
                                          Visibility="{Binding IsChecked, ElementName=MultiInstanceCheckBox, Converter={StaticResource BooleanToVisibilityConverter}}"
                                          Margin="20,0,0,15">
                                    <TextBlock Text="实例数量："
                                             VerticalAlignment="Center"
                                             FontWeight="Medium"
                                             Foreground="{DynamicResource SecondaryTextBrush}"
                                             Margin="0,0,10,0"/>
                                    <TextBox x:Name="InstanceCountTextBox"
                                           Text="2"
                                           Width="80"/>
                                </StackPanel>

                                <CheckBox x:Name="TestModeCheckBox"
                                        Content="测试模式（不实际启动）"
                                        FontSize="14"/>
                            </StackPanel>
                        </GroupBox>

                        <!-- 启动按钮 -->
                        <StackPanel Orientation="Horizontal"
                                  HorizontalAlignment="Center"
                                  Margin="0,0,0,20">
                            <Button x:Name="LaunchButton"
                                    Content="🚀 启动应用"
                                    Style="{StaticResource LauncherButtonStyle}"
                                    FontSize="16"
                                    FontWeight="Bold"
                                    Padding="25,12"
                                    Click="LaunchButton_Click"/>
                        </StackPanel>

                        <!-- 命令行预览 -->
                        <GroupBox Header="命令行预览">
                            <TextBox x:Name="CommandLinePreview"
                                   IsReadOnly="True"
                                   FontFamily="Consolas"
                                   FontSize="12"
                                   MinHeight="80"
                                   TextWrapping="Wrap"
                                   Background="#FFF8F8F8"
                                   Margin="15"/>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="2"
                Style="{StaticResource LauncherCardStyle}"
                Margin="0,10,0,0"
                Padding="20,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0"
                          Orientation="Horizontal"
                          VerticalAlignment="Center">
                    <StackPanel Orientation="Horizontal" Margin="0,0,25,0">
                        <TextBlock x:Name="AppStatusText"
                                 Text="Chat应用状态：检查中..."
                                 FontWeight="Medium"
                                 Foreground="{DynamicResource PrimaryTextBrush}"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="运行实例："
                                 FontWeight="Medium"
                                 Foreground="{DynamicResource SecondaryTextBrush}"/>
                        <TextBlock x:Name="RunningInstancesText"
                                 Text="0"
                                 FontWeight="Bold"
                                 Foreground="{DynamicResource PrimaryBrush}"
                                 Margin="5,0,0,0"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1"
                          Orientation="Horizontal"
                          VerticalAlignment="Center">
                    <Button x:Name="ViewLogsButton"
                            Content="📋 查看日志"
                            Style="{StaticResource LauncherButtonStyle}"
                            Margin="0,0,10,0"
                            Click="ViewLogsButton_Click"/>
                    <Button x:Name="CloseAllButton"
                            Content="❌ 关闭所有实例"
                            Style="{StaticResource LauncherButtonStyle}"
                            Click="CloseAllButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</hc:Window>