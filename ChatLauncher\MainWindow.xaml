<Window x:Class="ChatLauncher.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="聊天应用启动器" Height="600" Width="900"
        WindowStartupLocation="CenterScreen">
    <Window.Resources>
    <Style x:Key="ModernButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="#FF007ACC"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="15,8"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#FF005A9E"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="#FF004578"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="ConfigListBoxStyle" TargetType="ListBox">
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="#FFCCCCCC"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="Margin" Value="5"/>
    </Style>

    <Style x:Key="ConfigItemStyle" TargetType="ListBoxItem">
        <Setter Property="Padding" Value="10"/>
        <Setter Property="Margin" Value="2"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListBoxItem">
                    <Border Background="{TemplateBinding Background}" 
                                BorderThickness="1" 
                                BorderBrush="Transparent"
                                CornerRadius="3">
                        <ContentPresenter/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#FFF0F8FF"/>
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Background" Value="#FFE6F3FF"/>
                            <Setter Property="BorderBrush" Value="#FF007ACC"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#FF2D2D30" Padding="15,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🚀" FontSize="24" Margin="0,0,10,0"/>
                    <StackPanel>
                        <TextBlock Text="聊天应用启动器" 
                                 FontSize="18" 
                                 FontWeight="Bold" 
                                 Foreground="White"/>
                        <TextBlock x:Name="StatusText" 
                                 Text="就绪" 
                                 FontSize="12" 
                                 Foreground="#FFCCCCCC"/>
                    </StackPanel>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="RefreshButton" 
                            Content="🔄 刷新" 
                            Style="{StaticResource ModernButtonStyle}"
                            Click="RefreshButton_Click"/>
                    <Button x:Name="SettingsButton" 
                            Content="⚙️ 设置" 
                            Style="{StaticResource ModernButtonStyle}"
                            Click="SettingsButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="3*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：配置列表 -->
            <GroupBox Grid.Column="0" Header="启动配置" Margin="10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 搜索框 -->
                    <TextBox x:Name="SearchBox" 
                             Grid.Row="0"
                             Margin="5"
                             Padding="8"
                             Text=""
                             TextChanged="SearchBox_TextChanged">
                        <TextBox.Style>
                            <Style TargetType="TextBox">
                                <Setter Property="Foreground" Value="Black"/>
                                <Style.Triggers>
                                    <Trigger Property="Text" Value="">
                                        <Setter Property="Background">
                                            <Setter.Value>
                                                <VisualBrush Stretch="None" AlignmentX="Left">
                                                    <VisualBrush.Visual>
                                                        <TextBlock Text="🔍 搜索配置..." 
                                                                 Foreground="Gray" 
                                                                 Margin="5,0"/>
                                                    </VisualBrush.Visual>
                                                </VisualBrush>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </TextBox.Style>
                    </TextBox>

                    <!-- 配置列表 -->
                    <ListBox x:Name="ConfigListBox" 
                             Grid.Row="1"
                             Style="{StaticResource ConfigListBoxStyle}"
                             ItemContainerStyle="{StaticResource ConfigItemStyle}"
                             SelectionChanged="ConfigListBox_SelectionChanged">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Column="0" 
                                             Text="{Binding Icon}" 
                                             FontSize="16" 
                                             Margin="0,0,8,0"
                                             VerticalAlignment="Center"/>

                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="{Binding Name}" 
                                                 FontWeight="Bold" 
                                                 FontSize="14"/>
                                        <TextBlock Text="{Binding Description}" 
                                                 FontSize="11" 
                                                 Foreground="Gray" 
                                                 TextWrapping="Wrap"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                                        <TextBlock Text="⭐" 
                                                 Visibility="{Binding IsFavorite, Converter={x:Static BooleanToVisibilityConverter.Instance}}"
                                                 FontSize="12"
                                                 Margin="5,0"/>
                                        <TextBlock Text="{Binding UseCount}" 
                                                 FontSize="10" 
                                                 Foreground="Gray"
                                                 VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Grid>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>

                    <!-- 配置管理按钮 -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button x:Name="AddConfigButton" 
                                Content="➕ 新建" 
                                Style="{StaticResource ModernButtonStyle}"
                                Click="AddConfigButton_Click"/>
                        <Button x:Name="EditConfigButton" 
                                Content="✏️ 编辑" 
                                Style="{StaticResource ModernButtonStyle}"
                                Click="EditConfigButton_Click"/>
                        <Button x:Name="DeleteConfigButton" 
                                Content="🗑️ 删除" 
                                Style="{StaticResource ModernButtonStyle}"
                                Click="DeleteConfigButton_Click"/>
                    </StackPanel>
                </Grid>
            </GroupBox>

            <!-- 右侧：配置详情和启动选项 -->
            <GroupBox Grid.Column="1" Header="启动选项" Margin="10">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel x:Name="DetailsPanel" Margin="10">
                        <!-- 配置详情 -->
                        <GroupBox Header="配置详情" Margin="0,0,0,10">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="名称：" Margin="5"/>
                                <TextBlock x:Name="ConfigNameText" Grid.Row="0" Grid.Column="1" Margin="5" FontWeight="Bold"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="URL：" Margin="5"/>
                                <TextBlock x:Name="ConfigUrlText" Grid.Row="1" Grid.Column="1" Margin="5" TextWrapping="Wrap"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="代理：" Margin="5"/>
                                <TextBlock x:Name="ConfigProxyText" Grid.Row="2" Grid.Column="1" Margin="5"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="调试：" Margin="5"/>
                                <TextBlock x:Name="ConfigDebugText" Grid.Row="3" Grid.Column="1" Margin="5"/>

                                <TextBlock Grid.Row="4" Grid.Column="0" Text="使用次数：" Margin="5"/>
                                <TextBlock x:Name="ConfigUseCountText" Grid.Row="4" Grid.Column="1" Margin="5"/>
                            </Grid>
                        </GroupBox>

                        <!-- 启动选项 -->
                        <GroupBox Header="启动选项" Margin="0,0,0,10">
                            <StackPanel>
                                <CheckBox x:Name="MultiInstanceCheckBox" 
                                        Content="启动多个实例" 
                                        Margin="5"/>

                                <StackPanel Orientation="Horizontal" 
                                          Visibility="{Binding IsChecked, ElementName=MultiInstanceCheckBox, Converter={x:Static BooleanToVisibilityConverter.Instance}}">
                                    <TextBlock Text="实例数量：" VerticalAlignment="Center" Margin="20,5,5,5"/>
                                    <TextBox x:Name="InstanceCountTextBox" 
                                           Text="2" 
                                           Width="50" 
                                           Margin="5"/>
                                </StackPanel>

                                <CheckBox x:Name="TestModeCheckBox" 
                                        Content="测试模式（不实际启动）" 
                                        Margin="5"/>
                            </StackPanel>
                        </GroupBox>

                        <!-- 启动按钮 -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button x:Name="LaunchButton" 
                                    Content="🚀 启动应用" 
                                    Style="{StaticResource ModernButtonStyle}"
                                    FontSize="16"
                                    Padding="20,10"
                                    Click="LaunchButton_Click"/>
                        </StackPanel>

                        <!-- 命令行预览 -->
                        <GroupBox Header="命令行预览" Margin="0,10,0,0">
                            <TextBox x:Name="CommandLinePreview" 
                                   IsReadOnly="True" 
                                   TextWrapping="Wrap" 
                                   Background="#FFF8F8F8"
                                   FontFamily="Consolas"
                                   FontSize="11"
                                   Padding="8"/>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </GroupBox>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="#FFF0F0F0" BorderThickness="0,1,0,0" BorderBrush="#FFCCCCCC">
            <Grid Margin="10,5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock x:Name="AppStatusText" Text="Chat应用状态：检查中..." Margin="0,0,20,0"/>
                    <TextBlock x:Name="RunningInstancesText" Text="运行实例：0" Margin="0,0,20,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="ViewLogsButton" 
                            Content="📋 查看日志" 
                            Style="{StaticResource ModernButtonStyle}"
                            Click="ViewLogsButton_Click"/>
                    <Button x:Name="CloseAllButton" 
                            Content="❌ 关闭所有实例" 
                            Style="{StaticResource ModernButtonStyle}"
                            Click="CloseAllButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>