﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace Chat
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            //// 手动设置DataContext
            //DataContext = new ViewModels.MainWindowViewModel();

            // 初始化浏览器设置
            InitializeBrowser();
        }

        /// <summary>
        /// 初始化浏览器设置
        /// </summary>
        private void InitializeBrowser()
        {
            try
            {
                // 配置浏览器代理认证
                ConfigureBrowserProxy();

                // 显示代理状态
                UpdateProxyStatus();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化浏览器设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 配置浏览器代理
        /// </summary>
        private void ConfigureBrowserProxy()
        {
            var proxy = App.AppConfig?.Proxy;
            if (proxy?.Enabled == true && !string.IsNullOrEmpty(proxy.Username) && !string.IsNullOrEmpty(proxy.Password))
            {
                // 为主浏览器设置代理认证RequestHandler
                Browser.RequestHandler = new Services.ProxyAuthRequestHandler(proxy.Username, proxy.Password);
            }
        }

        /// <summary>
        /// 更新代理状态显示
        /// </summary>
        private void UpdateProxyStatus()
        {
            var proxy = App.AppConfig?.Proxy;
            if (proxy?.Enabled == true)
            {
                // 在标题栏显示代理信息
                this.Title += $" - 代理: {proxy.GetDisplayName()}";
            }
        }

        /// <summary>
        /// 加载指定URL
        /// </summary>
        public void LoadUrl(string url)
        {
            if (!string.IsNullOrEmpty(url))
            {
                try
                {
                    Services.CefSharpCompatibilityHelper.SafeLoadUrl(Browser, url);
                    UrlTextBox.Text = url;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"加载URL失败: {ex.Message}");
                }
            }
        }

        private void GoButton_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            if (!string.IsNullOrEmpty(UrlTextBox.Text))
            {
                LoadUrl(UrlTextBox.Text.Trim());
            }
        }

        private void MultiChatButton_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            // 打开多平台聊天窗口
            var multiChatWindow = new Views.MultiChatWindow();
            multiChatWindow.Show();
        }

        private void TestExtractionButton_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            // 打开测试提取窗口
            var testWindow = new TestChatExtraction();
            testWindow.Show();
        }

        private void FingerprintManagerButton_Click(object sender, System.Windows.RoutedEventArgs e)
        {
            // 打开指纹管理窗口
            var fingerprintWindow = new Views.FingerprintManagerWindow();
            fingerprintWindow.Show();
        }
    }
}
