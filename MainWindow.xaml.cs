﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using ChatLauncher.Models;
using ChatLauncher.Services;

namespace ChatLauncher
{
    public partial class MainWindow : HandyControl.Controls.Window
    {
        private readonly ChatAppLauncher _launcher;
        private readonly ConfigurationManager _configManager;
        private readonly ObservableCollection<LaunchConfig> _configs;
        private readonly ObservableCollection<LaunchConfig> _filteredConfigs;
        private readonly DispatcherTimer _statusTimer;

        public MainWindow()
        {
            InitializeComponent();

            _launcher = new ChatAppLauncher();
            _configManager = new ConfigurationManager();
            _configs = new ObservableCollection<LaunchConfig>();
            _filteredConfigs = new ObservableCollection<LaunchConfig>();

            ConfigListBox.ItemsSource = _filteredConfigs;

            // Initialize status update timer
            _statusTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(2)
            };
            _statusTimer.Tick += StatusTimer_Tick;
            _statusTimer.Start();

            LoadConfigs();
            UpdateStatus();
        }

        private void LoadConfigs()
        {
            try
            {
                // Load all configurations from configuration manager
                var allConfigs = _configManager.LoadConfigs();
                _configs.Clear();

                foreach (var config in allConfigs)
                {
                    _configs.Add(config);
                }

                FilterConfigs();

                // 调试信息
                System.Diagnostics.Debug.WriteLine($"LoadConfigs: 加载了 {_configs.Count} 个配置，过滤后显示 {_filteredConfigs.Count} 个");

                if (_filteredConfigs.Count > 0 && ConfigListBox.SelectedItem == null)
                {
                    ConfigListBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Failed to load configurations: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 强制刷新配置列表
        /// </summary>
        private void ForceRefreshConfigs()
        {
            // 保存当前选中的配置名称
            string selectedConfigName = (ConfigListBox.SelectedItem as LaunchConfig)?.Name;

            // 重新加载配置
            LoadConfigs();

            // 尝试恢复选中状态
            if (!string.IsNullOrEmpty(selectedConfigName))
            {
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    var configToSelect = _filteredConfigs.FirstOrDefault(c => c.Name == selectedConfigName);
                    if (configToSelect != null)
                    {
                        ConfigListBox.SelectedItem = configToSelect;
                        ConfigListBox.ScrollIntoView(configToSelect);
                    }
                }), System.Windows.Threading.DispatcherPriority.Background);
            }
        }

        private void FilterConfigs()
        {
            _filteredConfigs.Clear();

            string searchText = SearchBox.Text?.ToLower() ?? "";

            var filtered = string.IsNullOrEmpty(searchText)
                ? _configs
                : _configs.Where(c => c.Name.ToLower().Contains(searchText) ||
                                     c.Description?.ToLower().Contains(searchText) == true);

            foreach (var config in filtered.OrderByDescending(c => c.IsFavorite)
                                          .ThenByDescending(c => c.UseCount)
                                          .ThenBy(c => c.Name))
            {
                _filteredConfigs.Add(config);
            }
        }

        private async void UpdateStatus()
        {
            try
            {
                // Check Chat application status
                bool isAvailable = _launcher.IsChatAppAvailable();
                string version = _launcher.GetChatAppVersion();
                string path = _launcher.GetChatAppPath();

                AppStatusText.Text = isAvailable
                    ? $"Chat App: Available (v{version})"
                    : "Chat App: Not Available";

                StatusText.Text = isAvailable
                    ? $"Ready - {Path.GetFileName(path)}"
                    : "Chat.exe not found";

                // Check running instances
                var instances = _launcher.GetRunningChatInstances();
                RunningInstancesText.Text = instances.Length.ToString();

                // Update button states
                LaunchButton.IsEnabled = isAvailable && ConfigListBox.SelectedItem != null;
                CloseAllButton.IsEnabled = instances.Length > 0;
            }
            catch (Exception ex)
            {
                StatusText.Text = $"Status update failed: {ex.Message}";
            }
        }

        private void StatusTimer_Tick(object sender, EventArgs e)
        {
            UpdateStatus();
        }
    }
}
