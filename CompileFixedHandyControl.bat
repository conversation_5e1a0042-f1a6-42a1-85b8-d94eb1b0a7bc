@echo off
chcp 65001 > nul
echo ========================================
echo Compile Fixed HandyControl ChatLauncher
echo ========================================
echo.

echo Step 1: Clean previous build
if exist "ChatLauncher\bin" rmdir /s /q "ChatLauncher\bin"
if exist "ChatLauncher\obj" rmdir /s /q "ChatLauncher\obj"
echo Build folders cleaned.

echo.
echo Step 2: Verify HandyControl package
if exist "packages\HandyControl.3.5.1" (
    echo ✓ HandyControl package found
) else (
    echo ⚠ HandyControl package not found, trying to restore...
    nuget restore ChatLauncher\packages.config -PackagesDirectory packages
)

echo.
echo Step 3: Compile ChatLauncher with fixed HandyControl integration
echo Command: msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal
echo.

msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ ChatLauncher compiled successfully with fixed HandyControl!
    
    if exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
        echo ✓ Output file created: ChatLauncher\bin\Debug\ChatLauncher.exe
        
        echo.
        echo Checking dependencies:
        if exist "ChatLauncher\bin\Debug\HandyControl.dll" (
            echo ✓ HandyControl.dll found
        ) else (
            echo ⚠ HandyControl.dll missing, but may work with GAC
        )
        
        echo.
        echo File information:
        dir "ChatLauncher\bin\Debug\ChatLauncher.exe"
        
        echo.
        echo ========================================
        echo SUCCESS: Fixed HandyControl Integration!
        echo ========================================
        echo.
        echo Fixed issues:
        echo ✓ Removed unsupported Window properties
        echo ✓ Replaced unsupported controls with standard WPF
        echo ✓ Fixed Badge controls with custom styling
        echo ✓ Replaced UniformSpacingPanel with StackPanel
        echo ✓ Fixed SearchBar and CodeBox controls
        echo ✓ Maintained HandyControl theme and styling
        echo.
        echo ChatLauncher now features:
        echo ✓ HandyControl Window and theming
        echo ✓ Compatible control implementations
        echo ✓ Modern card-based layout
        echo ✓ Enhanced visual styling
        echo ✓ Stable and working UI
        echo.
        
        set /p choice=Do you want to start the fixed ChatLauncher? (y/n): 
        if /i "%choice%"=="y" (
            echo.
            echo Starting ChatLauncher with fixed HandyControl UI...
            start ChatLauncher\bin\Debug\ChatLauncher.exe
            echo.
            echo ✓ ChatLauncher started! The interface should now work properly.
        )
    ) else (
        echo ✗ Output file not found
    )
) else (
    echo.
    echo ✗ Compilation failed!
    echo.
    echo If you still see errors, they might be:
    echo 1. Missing HandyControl package - run: nuget install HandyControl
    echo 2. XAML syntax errors - check the error messages above
    echo 3. Control compatibility issues - some controls may need further fixes
    echo.
    echo Please check the error messages above for specific issues.
)

echo.
echo ========================================
echo Compilation Complete
echo ========================================
pause
