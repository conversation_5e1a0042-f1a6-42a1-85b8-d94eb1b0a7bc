using Chat.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Chat.Services
{
    /// <summary>
    /// 命令行参数解析器
    /// </summary>
    public class CommandLineParser
    {
        /// <summary>
        /// 应用程序配置
        /// </summary>
        public class AppConfig
        {
            /// <summary>
            /// 代理配置
            /// </summary>
            public ProxyConfig Proxy { get; set; } = new ProxyConfig();

            /// <summary>
            /// 是否启用调试模式
            /// </summary>
            public bool Debug { get; set; } = false;

            /// <summary>
            /// 启动时自动打开的URL
            /// </summary>
            public string StartUrl { get; set; } = "";

            /// <summary>
            /// 指定的指纹配置ID
            /// </summary>
            public string FingerprintId { get; set; } = "";

            /// <summary>
            /// 是否显示帮助信息
            /// </summary>
            public bool ShowHelp { get; set; } = false;

            /// <summary>
            /// 用户数据目录
            /// </summary>
            public string UserDataDir { get; set; } = "";

            /// <summary>
            /// 缓存目录
            /// </summary>
            public string CacheDir { get; set; } = "";
        }

        /// <summary>
        /// 解析命令行参数
        /// </summary>
        public static AppConfig Parse(string[] args)
        {
            var config = new AppConfig();

            if (args == null || args.Length == 0)
            {
                return config;
            }

            try
            {
                for (int i = 0; i < args.Length; i++)
                {
                    var arg = args[i].ToLower();

                    switch (arg)
                    {
                        case "--proxy":
                        case "-p":
                            if (i + 1 < args.Length)
                            {
                                config.Proxy = ProxyConfig.ParseFromString(args[i + 1]);
                                i++; // 跳过下一个参数
                            }
                            break;

                        case "--debug":
                        case "-d":
                            config.Debug = true;
                            break;

                        case "--url":
                        case "-u":
                            if (i + 1 < args.Length)
                            {
                                config.StartUrl = args[i + 1];
                                i++; // 跳过下一个参数
                            }
                            break;

                        case "--fingerprint":
                        case "-f":
                            if (i + 1 < args.Length)
                            {
                                config.FingerprintId = args[i + 1];
                                i++; // 跳过下一个参数
                            }
                            break;

                        case "--user-data-dir":
                            if (i + 1 < args.Length)
                            {
                                config.UserDataDir = args[i + 1];
                                i++; // 跳过下一个参数
                            }
                            break;

                        case "--cache-dir":
                            if (i + 1 < args.Length)
                            {
                                config.CacheDir = args[i + 1];
                                i++; // 跳过下一个参数
                            }
                            break;

                        case "--help":
                        case "-h":
                        case "/?":
                            config.ShowHelp = true;
                            break;

                        default:
                            // 如果参数不以--或-开头，可能是URL
                            if (!arg.StartsWith("-") && Uri.IsWellFormedUriString(arg, UriKind.Absolute))
                            {
                                config.StartUrl = args[i];
                            }
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析命令行参数失败: {ex.Message}");
            }

            return config;
        }

        /// <summary>
        /// 获取帮助信息
        /// </summary>
        public static string GetHelpText()
        {
            return @"
聊天集成应用 - 命令行参数说明

用法: Chat.exe [选项]

代理选项:
  --proxy, -p <proxy_url>     设置代理服务器
                              格式: http://host:port
                                   https://host:port  
                                   socks5://host:port
                                   *********************:port

应用选项:
  --url, -u <url>            启动时打开指定URL
  --fingerprint, -f <id>     使用指定的指纹配置ID
  --debug, -d                启用调试模式
  --user-data-dir <path>     指定用户数据目录
  --cache-dir <path>         指定缓存目录
  --help, -h                 显示此帮助信息

代理示例:
  Chat.exe --proxy http://127.0.0.1:8080
  Chat.exe --proxy socks5://user:<EMAIL>:1080
  Chat.exe -p http://proxy.company.com:3128

完整示例:
  Chat.exe --proxy http://127.0.0.1:8080 --url https://web.whatsapp.com --debug
  Chat.exe -p socks5://127.0.0.1:1080 -f fingerprint_001 -d

注意:
  - 代理配置会应用到所有浏览器实例
  - 支持HTTP、HTTPS、SOCKS4、SOCKS5代理
  - 支持用户名密码认证
  - 如果不指定代理，将使用系统默认网络设置
";
        }

        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        public static bool ValidateConfig(AppConfig config, out string errorMessage)
        {
            errorMessage = "";

            // 验证代理配置
            if (config.Proxy.Enabled && !config.Proxy.IsValid())
            {
                errorMessage = "代理配置无效：请检查主机地址和端口号";
                return false;
            }

            // 验证URL格式
            if (!string.IsNullOrEmpty(config.StartUrl) && 
                !Uri.IsWellFormedUriString(config.StartUrl, UriKind.Absolute))
            {
                errorMessage = "启动URL格式无效";
                return false;
            }

            // 验证目录路径
            if (!string.IsNullOrEmpty(config.UserDataDir))
            {
                try
                {
                    var fullPath = System.IO.Path.GetFullPath(config.UserDataDir);
                }
                catch
                {
                    errorMessage = "用户数据目录路径无效";
                    return false;
                }
            }

            if (!string.IsNullOrEmpty(config.CacheDir))
            {
                try
                {
                    var fullPath = System.IO.Path.GetFullPath(config.CacheDir);
                }
                catch
                {
                    errorMessage = "缓存目录路径无效";
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 获取配置摘要信息
        /// </summary>
        public static string GetConfigSummary(AppConfig config)
        {
            var summary = new List<string>();

            if (config.Proxy.Enabled)
            {
                summary.Add($"代理: {config.Proxy.GetDisplayName()}");
            }
            else
            {
                summary.Add("代理: 未设置");
            }

            if (!string.IsNullOrEmpty(config.StartUrl))
            {
                summary.Add($"启动URL: {config.StartUrl}");
            }

            if (!string.IsNullOrEmpty(config.FingerprintId))
            {
                summary.Add($"指纹ID: {config.FingerprintId}");
            }

            if (config.Debug)
            {
                summary.Add("调试模式: 已启用");
            }

            return string.Join(Environment.NewLine, summary);
        }
    }
}
