@echo off
echo ========================================
echo 启动聊天应用启动器
echo ========================================
echo.

REM 检查ChatLauncher是否存在
if not exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
    echo 错误：找不到ChatLauncher.exe
    echo 请先编译ChatLauncher项目！
    echo.
    echo 编译方法：
    echo 1. 在Visual Studio中右键ChatLauncher项目 -> 生成
    echo 2. 或者运行 msbuild ChatLauncher\ChatLauncher.csproj
    echo.
    pause
    exit /b 1
)

echo 启动ChatLauncher...
start ChatLauncher\bin\Debug\ChatLauncher.exe

echo.
echo ChatLauncher已启动！
echo 现在您可以通过图形界面管理Chat应用的启动。
echo.
echo 按任意键退出...
pause > nul
