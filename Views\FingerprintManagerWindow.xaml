<Window x:Class="Chat.Views.FingerprintManagerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="浏览器指纹管理器" Height="700" Width="1200"
        WindowStartupLocation="CenterScreen">
    <Window.Resources>
        <!-- 转换器 -->
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>

        <!-- 自定义转换器需要在代码中实现 -->
        <local:BoolToColorConverter x:Key="BoolToColorConverter"/>
        <local:BoolToStatusConverter x:Key="BoolToStatusConverter"/>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 工具栏 -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="10">
            <StackPanel Orientation="Horizontal">
                <Button x:Name="NewFingerprintButton" Content="新建指纹" Margin="0,0,10,0" 
                        Background="#3498DB" Foreground="White" Padding="10,5" Click="NewFingerprintButton_Click"/>
                <Button x:Name="RandomFingerprintButton" Content="随机生成" Margin="0,0,10,0"
                        Background="#E67E22" Foreground="White" Padding="10,5" Click="RandomFingerprintButton_Click"/>
                <Button x:Name="ImportButton" Content="导入配置" Margin="0,0,10,0"
                        Background="#27AE60" Foreground="White" Padding="10,5" Click="ImportButton_Click"/>
                <Button x:Name="ExportButton" Content="导出配置" Margin="0,0,10,0"
                        Background="#8E44AD" Foreground="White" Padding="10,5" Click="ExportButton_Click"/>
                <Button x:Name="TestFingerprintButton" Content="测试指纹" Margin="0,0,10,0"
                        Background="#E74C3C" Foreground="White" Padding="10,5" Click="TestFingerprintButton_Click"/>
                <TextBlock Text="指纹管理器" Foreground="White" VerticalAlignment="Center" Margin="20,0,0,0" FontSize="16" FontWeight="Bold"/>
            </StackPanel>
        </Border>
        
        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- 指纹列表 -->
            <Border Grid.Column="0" Background="#ECF0F1" Padding="10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Text="指纹配置列表" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                    
                    <ListBox x:Name="FingerprintListBox" Grid.Row="1" Background="White" 
                             SelectionChanged="FingerprintListBox_SelectionChanged">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Background="White" Padding="10" Margin="0,2" CornerRadius="5"
                                        BorderBrush="#BDC3C7" BorderThickness="1">
                                    <StackPanel>
                                        <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="12"/>
                                        <TextBlock Text="{Binding Description}" FontSize="10" 
                                                  Foreground="#7F8C8D" TextWrapping="Wrap"/>
                                        <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                            <TextBlock Text="创建时间: " FontSize="9" Foreground="#95A5A6"/>
                                            <TextBlock Text="{Binding CreatedAt, StringFormat=yyyy-MM-dd}" 
                                                      FontSize="9" Foreground="#95A5A6"/>
                                        </StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                                            <Ellipse Width="8" Height="8" Margin="0,0,5,0">
                                                <Ellipse.Fill>
                                                    <SolidColorBrush Color="{Binding IsEnabled, Converter={StaticResource BoolToColorConverter}}"/>
                                                </Ellipse.Fill>
                                            </Ellipse>
                                            <TextBlock Text="{Binding IsEnabled, Converter={StaticResource BoolToStatusConverter}}" 
                                                      FontSize="9"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Grid>
            </Border>
            
            <!-- 分隔线 -->
            <GridSplitter Grid.Column="1" Background="#BDC3C7" HorizontalAlignment="Stretch"/>
            
            <!-- 指纹详情编辑区域 -->
            <ScrollViewer Grid.Column="2" VerticalScrollBarVisibility="Auto">
                <Border Padding="20">
                    <StackPanel x:Name="FingerprintDetailsPanel">
                        <!-- 基本信息 -->
                        <GroupBox Header="基本信息" Margin="0,0,0,15">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="指纹名称:" VerticalAlignment="Center"/>
                                <TextBox x:Name="FingerprintNameTextBox" Grid.Row="0" Grid.Column="1" Margin="5,2"/>
                                
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="描述:" VerticalAlignment="Top" Margin="0,5,0,0"/>
                                <TextBox x:Name="FingerprintDescriptionTextBox" Grid.Row="1" Grid.Column="1" 
                                        Margin="5,2" Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>
                                
                                <TextBlock Grid.Row="2" Grid.Column="0" Text="启用状态:" VerticalAlignment="Center"/>
                                <CheckBox x:Name="FingerprintEnabledCheckBox" Grid.Row="2" Grid.Column="1" 
                                         Content="启用此指纹配置" Margin="5,2"/>
                            </Grid>
                        </GroupBox>
                        
                        <!-- 浏览器信息 -->
                        <GroupBox Header="浏览器信息" Margin="0,0,0,15">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="User Agent:" VerticalAlignment="Top" Margin="0,5,0,0"/>
                                <TextBox x:Name="UserAgentTextBox" Grid.Row="0" Grid.Column="1" 
                                        Margin="5,2" Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>
                                
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="语言:" VerticalAlignment="Center"/>
                                <TextBox x:Name="LanguageTextBox" Grid.Row="1" Grid.Column="1" Margin="5,2"/>
                                
                                <TextBlock Grid.Row="2" Grid.Column="0" Text="平台:" VerticalAlignment="Center"/>
                                <ComboBox x:Name="PlatformComboBox" Grid.Row="2" Grid.Column="1" Margin="5,2">
                                    <ComboBoxItem Content="Win32"/>
                                    <ComboBoxItem Content="MacIntel"/>
                                    <ComboBoxItem Content="Linux x86_64"/>
                                </ComboBox>
                                
                                <TextBlock Grid.Row="3" Grid.Column="0" Text="时区:" VerticalAlignment="Center"/>
                                <ComboBox x:Name="TimezoneComboBox" Grid.Row="3" Grid.Column="1" Margin="5,2">
                                    <ComboBoxItem Content="Asia/Shanghai" Tag="-480"/>
                                    <ComboBoxItem Content="America/New_York" Tag="300"/>
                                    <ComboBoxItem Content="Europe/London" Tag="0"/>
                                    <ComboBoxItem Content="Asia/Tokyo" Tag="-540"/>
                                </ComboBox>
                                
                                <TextBlock Grid.Row="4" Grid.Column="0" Text="Do Not Track:" VerticalAlignment="Center"/>
                                <ComboBox x:Name="DoNotTrackComboBox" Grid.Row="4" Grid.Column="1" Margin="5,2">
                                    <ComboBoxItem Content="1" Tag="1"/>
                                    <ComboBoxItem Content="0" Tag="0"/>
                                    <ComboBoxItem Content="null" Tag=""/>
                                </ComboBox>
                            </Grid>
                        </GroupBox>
                        
                        <!-- 屏幕信息 -->
                        <GroupBox Header="屏幕信息" Margin="0,0,0,15">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="屏幕宽度:" VerticalAlignment="Center"/>
                                <TextBox x:Name="ScreenWidthTextBox" Grid.Row="0" Grid.Column="1" Margin="5,2"/>
                                
                                <TextBlock Grid.Row="0" Grid.Column="2" Text="屏幕高度:" VerticalAlignment="Center"/>
                                <TextBox x:Name="ScreenHeightTextBox" Grid.Row="0" Grid.Column="3" Margin="5,2"/>
                                
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="颜色深度:" VerticalAlignment="Center"/>
                                <ComboBox x:Name="ColorDepthComboBox" Grid.Row="1" Grid.Column="1" Margin="5,2">
                                    <ComboBoxItem Content="24"/>
                                    <ComboBoxItem Content="32"/>
                                </ComboBox>
                                
                                <TextBlock Grid.Row="1" Grid.Column="2" Text="像素比:" VerticalAlignment="Center"/>
                                <TextBox x:Name="DevicePixelRatioTextBox" Grid.Row="1" Grid.Column="3" Margin="5,2"/>
                            </Grid>
                        </GroupBox>
                        
                        <!-- 硬件信息 -->
                        <GroupBox Header="硬件信息" Margin="0,0,0,15">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="CPU核心数:" VerticalAlignment="Center"/>
                                <ComboBox x:Name="CpuCoresComboBox" Grid.Row="0" Grid.Column="1" Margin="5,2">
                                    <ComboBoxItem Content="2"/>
                                    <ComboBoxItem Content="4"/>
                                    <ComboBoxItem Content="6"/>
                                    <ComboBoxItem Content="8"/>
                                    <ComboBoxItem Content="12"/>
                                    <ComboBoxItem Content="16"/>
                                </ComboBox>
                                
                                <TextBlock Grid.Row="0" Grid.Column="2" Text="内存(GB):" VerticalAlignment="Center"/>
                                <ComboBox x:Name="MemoryComboBox" Grid.Row="0" Grid.Column="3" Margin="5,2">
                                    <ComboBoxItem Content="4"/>
                                    <ComboBoxItem Content="8"/>
                                    <ComboBoxItem Content="16"/>
                                    <ComboBoxItem Content="32"/>
                                </ComboBox>
                                
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="设备名称:" VerticalAlignment="Center"/>
                                <TextBox x:Name="DeviceNameTextBox" Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="3" Margin="5,2"/>
                            </Grid>
                        </GroupBox>
                        
                        <!-- 高级选项 -->
                        <Expander Header="高级选项" Margin="0,0,0,15">
                            <StackPanel Margin="10">
                                <!-- Canvas指纹 -->
                                <GroupBox Header="Canvas指纹" Margin="0,0,0,10">
                                    <Grid Margin="10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" Text="噪声级别:" VerticalAlignment="Center"/>
                                        <ComboBox x:Name="CanvasNoiseComboBox" Grid.Column="1" Margin="5,2">
                                            <ComboBoxItem Content="None"/>
                                            <ComboBoxItem Content="Low"/>
                                            <ComboBoxItem Content="Medium"/>
                                            <ComboBoxItem Content="High"/>
                                        </ComboBox>
                                    </Grid>
                                </GroupBox>
                                
                                <!-- 音频指纹 -->
                                <GroupBox Header="音频指纹" Margin="0,0,0,10">
                                    <Grid Margin="10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" Text="噪声级别:" VerticalAlignment="Center"/>
                                        <ComboBox x:Name="AudioNoiseComboBox" Grid.Column="1" Margin="5,2">
                                            <ComboBoxItem Content="None"/>
                                            <ComboBoxItem Content="Low"/>
                                            <ComboBoxItem Content="Medium"/>
                                            <ComboBoxItem Content="High"/>
                                        </ComboBox>
                                    </Grid>
                                </GroupBox>
                                
                                <!-- WebRTC设置 -->
                                <GroupBox Header="WebRTC设置" Margin="0,0,0,10">
                                    <StackPanel Margin="10">
                                        <CheckBox x:Name="WebRTCEnabledCheckBox" Content="启用WebRTC" Margin="0,0,0,5"/>
                                        <CheckBox x:Name="BlockLocalIPCheckBox" Content="阻止本地IP泄露" Margin="0,0,0,5"/>
                                        <CheckBox x:Name="BlockPublicIPCheckBox" Content="阻止公网IP泄露"/>
                                    </StackPanel>
                                </GroupBox>
                            </StackPanel>
                        </Expander>
                    </StackPanel>
                </Border>
            </ScrollViewer>
        </Grid>
        
        <!-- 底部按钮栏 -->
        <Border Grid.Row="2" Background="#34495E" Padding="10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button x:Name="SaveButton" Content="保存" Margin="0,0,10,0"
                        Background="#27AE60" Foreground="White" Padding="15,5" Click="SaveButton_Click"/>
                <Button x:Name="DeleteButton" Content="删除" Margin="0,0,10,0"
                        Background="#E74C3C" Foreground="White" Padding="15,5" Click="DeleteButton_Click"/>
                <Button x:Name="CloseButton" Content="关闭" 
                        Background="#95A5A6" Foreground="White" Padding="15,5" Click="CloseButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
