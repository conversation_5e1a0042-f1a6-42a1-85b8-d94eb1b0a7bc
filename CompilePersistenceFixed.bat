@echo off
chcp 65001 > nul
echo ========================================
echo Compile with Fixed Configuration Persistence
echo ========================================
echo.

echo Step 1: Clean previous build
if exist "ChatLauncher\bin" rmdir /s /q "ChatLauncher\bin"
if exist "ChatLauncher\obj" rmdir /s /q "ChatLauncher\obj"
echo Build folders cleaned.

echo.
echo Step 2: Configuration persistence fixes applied:
echo ✓ Fixed default config regeneration issue
echo ✓ Only create default configs if file doesn't exist
echo ✓ Added CloneForEdit method to preserve original names
echo ✓ Fixed proxy configuration persistence
echo ✓ Enhanced debugging information for save operations
echo ✓ Proper handling of default vs user configurations

echo.
echo Step 3: Key fixes implemented:
echo ✓ InitializeDefaultConfigs: Only create if not exists
echo ✓ CloneForEdit: Preserves original configuration data
echo ✓ Default config editing creates user configs properly
echo ✓ User config editing updates existing configs
echo ✓ Proxy changes are properly saved and loaded
echo ✓ Configuration state debugging added

echo.
echo Step 4: Compile ChatLauncher with fixed persistence
echo Command: msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal
echo.

msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ ChatLauncher compiled successfully with fixed persistence!
    
    if exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
        echo ✓ Output file created: ChatLauncher\bin\Debug\ChatLauncher.exe
        
        echo.
        echo ========================================
        echo SUCCESS: Configuration Persistence Fixed!
        echo ========================================
        echo.
        echo 🔧 Fixed Issues:
        echo.
        echo 1. 🔄 Default Configuration Overwriting:
        echo    • Problem: Default configs regenerated on every startup
        echo    • Fix: Only create default configs if file doesn't exist
        echo    • Result: User modifications to configs persist
        echo.
        echo 2. 📝 Configuration Cloning:
        echo    • Problem: Clone() method added "(副本)" to names
        echo    • Fix: Added CloneForEdit() method preserving names
        echo    • Result: Editing configs maintains original names
        echo.
        echo 3. 💾 Proxy Configuration Persistence:
        echo    • Problem: Proxy changes reverted on restart
        echo    • Fix: Proper save/load cycle for user configs
        echo    • Result: Proxy port changes persist correctly
        echo.
        echo 4. 🎯 Default vs User Config Handling:
        echo    • Problem: Confusion between default and user configs
        echo    • Fix: Clear separation and proper creation logic
        echo    • Result: Default configs create user copies when edited
        echo.
        
        set /p choice=Launch ChatLauncher to test fixed persistence? (y/n): 
        if /i "%choice%"=="y" (
            echo.
            echo Starting ChatLauncher with fixed configuration persistence...
            echo.
            echo 🎉 Test the following persistence scenarios:
            echo.
            echo 1. 🔧 Edit Default Configuration:
            echo    • Select "代理测试 (HTTP)" configuration
            echo    • Click "编辑" button
            echo    • Change proxy port from 8080 to 42000
            echo    • Click "保存" button
            echo    • Should see "已基于默认配置创建新配置" message
            echo    • New user config should appear in list
            echo.
            echo 2. 🔄 Test Persistence:
            echo    • After editing and saving the proxy config
            echo    • Close the application completely
            echo    • Reopen ChatLauncher
            echo    • Select the modified configuration
            echo    • Proxy should still show 42000, not 8080
            echo.
            echo 3. ✏️ Edit User Configuration:
            echo    • Select a user-created configuration
            echo    • Click "编辑" button
            echo    • Modify any parameters
            echo    • Save changes
            echo    • Should see "配置已更新" message
            echo    • Changes should persist across restarts
            echo.
            echo 4. 🎯 Verify Default Config Integrity:
            echo    • Original "代理测试 (HTTP)" should still exist
            echo    • Should still show original 8080 port
            echo    • Default configs should remain unchanged
            echo    • User configs should be separate entries
            echo.
            echo 5. 📊 Multiple Edit Cycles:
            echo    • Edit same config multiple times
            echo    • Change proxy port: 42000 → 43000 → 44000
            echo    • Each change should persist
            echo    • No reversion to original values
            echo.
            start ChatLauncher\bin\Debug\ChatLauncher.exe
            echo.
            echo ✅ ChatLauncher launched with fixed persistence!
            echo.
            echo 📂 Configuration files structure:
            echo    ChatLauncher\bin\Debug\Configs\
            echo    ├── default_configs.xml (original defaults, unchanged)
            echo    └── configs.xml (user modifications, persistent)
            echo.
            echo 🎯 Expected Behavior:
            echo • Proxy port changes from 8080 to 42000 persist
            echo • Default configs remain unchanged
            echo • User configs save and load correctly
            echo • No configuration data loss on restart
            echo • Clear separation between default and user configs
        ) else (
            echo.
            echo ChatLauncher with fixed persistence is ready!
            echo Execute: ChatLauncher\bin\Debug\ChatLauncher.exe
        )
    ) else (
        echo ✗ Output file not found
        echo This indicates a compilation issue.
    )
) else (
    echo.
    echo ✗ Compilation failed!
    echo Please check the error messages above for specific issues.
)

echo.
echo ========================================
echo Configuration Persistence Fix Summary
echo ========================================
echo.
echo 🔧 Technical Fixes Applied:
echo.
echo 1. 📁 ConfigurationManager.InitializeDefaultConfigs():
echo    • Before: Always regenerated default configs
echo    • After: Only create if file doesn't exist
echo    • Impact: Preserves user modifications
echo.
echo 2. 🔄 LaunchConfig.CloneForEdit():
echo    • Before: Clone() added "(副本)" suffix
echo    • After: CloneForEdit() preserves original name
echo    • Impact: Editing maintains config identity
echo.
echo 3. 💾 Configuration Save Logic:
echo    • Before: Unclear default vs user handling
echo    • After: Clear separation and proper persistence
echo    • Impact: Reliable configuration storage
echo.
echo 4. 🎯 Edit Flow Improvements:
echo    • Default config edit → Creates user config
echo    • User config edit → Updates existing config
echo    • Proper IsDefault flag handling
echo    • Enhanced debugging information
echo.
echo 📊 Persistence Flow:
echo.
echo 1. User edits default config (e.g., proxy port 8080→42000)
echo 2. System creates new user config with modified values
echo 3. User config saved to configs.xml
echo 4. Default config remains unchanged in default_configs.xml
echo 5. On restart: Both configs load correctly
echo 6. User sees both original default and modified user config
echo.
echo 🏆 Quality Improvements:
echo    • Reliable configuration persistence
echo    • No data loss on application restart
echo    • Clear default vs user config separation
echo    • Proper proxy configuration handling
echo    • Enhanced debugging capabilities
echo.
echo 🎯 Test Results Expected:
echo    • Proxy port 42000 persists after restart
echo    • Default "代理测试 (HTTP)" still shows 8080
echo    • User config shows modified 42000 port
echo    • Multiple edit cycles work correctly
echo    • No configuration reversion issues
echo.
echo Configuration persistence is now fully reliable! 🎉
pause
