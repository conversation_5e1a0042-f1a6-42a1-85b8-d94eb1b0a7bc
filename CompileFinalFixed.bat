@echo off
chcp 65001 > nul
echo ========================================
echo Final Fixed ChatLauncher Compilation
echo ========================================
echo.

echo Step 1: Clean previous build
if exist "ChatLauncher\bin" rmdir /s /q "ChatLauncher\bin"
if exist "ChatLauncher\obj" rmdir /s /q "ChatLauncher\obj"
echo Build folders cleaned.

echo.
echo Step 2: Verify project files
echo Checking critical files...

if exist "ChatLauncher\ChatLauncher.csproj" (
    echo ✓ ChatLauncher.csproj found
) else (
    echo ✗ ChatLauncher.csproj missing
    pause
    exit /b 1
)

if exist "ChatLauncher\MainWindow.xaml" (
    echo ✓ MainWindow.xaml found
) else (
    echo ✗ MainWindow.xaml missing
    pause
    exit /b 1
)

if exist "ChatLauncher\App.xaml" (
    echo ✓ App.xaml found
) else (
    echo ✗ App.xaml missing
    pause
    exit /b 1
)

echo.
echo Step 3: Check HandyControl package (optional)
if exist "packages\HandyControl.3.5.1" (
    echo ✓ HandyControl package available
) else (
    echo ⚠ HandyControl package not found, but will try to compile anyway
    echo   (HandyControl features may be limited)
)

echo.
echo Step 4: Compile ChatLauncher
echo Command: msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal
echo.

msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ ChatLauncher compiled successfully!
    
    if exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
        echo ✓ Output file created: ChatLauncher\bin\Debug\ChatLauncher.exe
        
        echo.
        echo Checking output files:
        dir "ChatLauncher\bin\Debug\*.exe" /b
        dir "ChatLauncher\bin\Debug\*.dll" /b | findstr /i "handy" 2>nul
        if %ERRORLEVEL% EQU 0 (
            echo ✓ HandyControl dependencies found
        ) else (
            echo ⚠ HandyControl dependencies not found (may use fallback styling)
        )
        
        echo.
        echo ========================================
        echo SUCCESS: Final Compilation Complete!
        echo ========================================
        echo.
        echo Fixed issues:
        echo ✓ Removed unsupported HandyControl properties
        echo ✓ Fixed converter references
        echo ✓ Replaced incompatible controls with standard WPF
        echo ✓ Unified button styling
        echo ✓ Maintained modern appearance
        echo ✓ Ensured C# 7.3 compatibility
        echo ✓ Fixed Chat.exe path resolution
        echo.
        echo Features:
        echo ✓ Modern HandyControl-inspired interface
        echo ✓ Card-based layout design
        echo ✓ Professional button styling
        echo ✓ Responsive layout
        echo ✓ Chat application management
        echo ✓ Multi-instance support
        echo ✓ Configuration management
        echo ✓ Status monitoring
        echo.
        
        set /p choice=Do you want to start ChatLauncher now? (y/n): 
        if /i "%choice%"=="y" (
            echo.
            echo Starting ChatLauncher...
            echo.
            echo If the application starts successfully, you should see:
            echo - Modern card-based interface
            echo - Chat application status detection
            echo - Configuration list with predefined options
            echo - Launch controls and options
            echo.
            start ChatLauncher\bin\Debug\ChatLauncher.exe
            echo.
            echo ✓ ChatLauncher started!
            echo.
            echo If you see any issues:
            echo 1. Check that Chat.exe exists in bin\x86\Debug\
            echo 2. Verify all dependencies are present
            echo 3. Check the application logs for errors
        )
    ) else (
        echo ✗ Output file not found
        echo This indicates a compilation issue.
    )
) else (
    echo.
    echo ✗ Compilation failed!
    echo.
    echo Common remaining issues might be:
    echo 1. Missing project references
    echo 2. XAML syntax errors
    echo 3. C# compilation errors
    echo 4. Missing dependencies
    echo.
    echo Please check the error messages above for specific details.
    echo.
    echo If you need help:
    echo 1. Check the exact error message
    echo 2. Verify all required files are present
    echo 3. Ensure .NET Framework 4.8.1 is installed
    echo 4. Try cleaning and rebuilding the solution
)

echo.
echo ========================================
echo Compilation Process Complete
echo ========================================
pause
