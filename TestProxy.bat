@echo off
echo ========================================
echo 聊天集成应用 - 代理功能测试
echo ========================================
echo.

echo 请选择测试场景：
echo 1. 无代理启动（默认）
echo 2. HTTP代理测试
echo 3. SOCKS5代理测试
echo 4. 带认证的HTTP代理测试
echo 5. 带认证的SOCKS5代理测试
echo 6. 代理 + WhatsApp Web
echo 7. 代理 + Telegram Web
echo 8. 显示帮助信息
echo 9. 退出
echo.

set /p choice=请输入选择 (1-9): 

if "%choice%"=="1" goto no_proxy
if "%choice%"=="2" goto http_proxy
if "%choice%"=="3" goto socks5_proxy
if "%choice%"=="4" goto http_auth_proxy
if "%choice%"=="5" goto socks5_auth_proxy
if "%choice%"=="6" goto whatsapp_proxy
if "%choice%"=="7" goto telegram_proxy
if "%choice%"=="8" goto show_help
if "%choice%"=="9" goto exit

echo 无效选择，使用默认启动...
goto no_proxy

:no_proxy
echo.
echo 启动应用（无代理）...
Chat.exe --debug
goto end

:http_proxy
echo.
echo 请输入HTTP代理地址（例如：127.0.0.1:8080）
set /p proxy_addr=代理地址: 
if "%proxy_addr%"=="" set proxy_addr=127.0.0.1:8080
echo 启动应用（HTTP代理：%proxy_addr%）...
Chat.exe --proxy http://%proxy_addr% --debug
goto end

:socks5_proxy
echo.
echo 请输入SOCKS5代理地址（例如：127.0.0.1:1080）
set /p proxy_addr=代理地址: 
if "%proxy_addr%"=="" set proxy_addr=127.0.0.1:1080
echo 启动应用（SOCKS5代理：%proxy_addr%）...
Chat.exe --proxy socks5://%proxy_addr% --debug
goto end

:http_auth_proxy
echo.
echo 请输入HTTP代理信息：
set /p proxy_addr=代理地址（例如：proxy.company.com:8080）: 
set /p username=用户名: 
set /p password=密码: 
if "%proxy_addr%"=="" set proxy_addr=proxy.company.com:8080
if "%username%"=="" set username=user
if "%password%"=="" set password=pass
echo 启动应用（HTTP代理认证：%username%@%proxy_addr%）...
Chat.exe --proxy http://%username%:%password%@%proxy_addr% --debug
goto end

:socks5_auth_proxy
echo.
echo 请输入SOCKS5代理信息：
set /p proxy_addr=代理地址（例如：socks.server.com:1080）: 
set /p username=用户名: 
set /p password=密码: 
if "%proxy_addr%"=="" set proxy_addr=socks.server.com:1080
if "%username%"=="" set username=user
if "%password%"=="" set password=pass
echo 启动应用（SOCKS5代理认证：%username%@%proxy_addr%）...
Chat.exe --proxy socks5://%username%:%password%@%proxy_addr% --debug
goto end

:whatsapp_proxy
echo.
echo 请输入代理地址（例如：127.0.0.1:8080）
set /p proxy_addr=代理地址: 
if "%proxy_addr%"=="" set proxy_addr=127.0.0.1:8080
echo 启动应用（代理 + WhatsApp Web）...
Chat.exe --proxy http://%proxy_addr% --url https://web.whatsapp.com --debug
goto end

:telegram_proxy
echo.
echo 请输入代理地址（例如：127.0.0.1:1080）
set /p proxy_addr=代理地址: 
if "%proxy_addr%"=="" set proxy_addr=127.0.0.1:1080
echo 启动应用（代理 + Telegram Web）...
Chat.exe --proxy socks5://%proxy_addr% --url https://web.telegram.org --debug
goto end

:show_help
echo.
echo 显示帮助信息...
Chat.exe --help
echo.
pause
goto start

:exit
echo 退出测试...
goto end

:end
echo.
echo 测试完成！
pause

:start
cls
goto :eof
