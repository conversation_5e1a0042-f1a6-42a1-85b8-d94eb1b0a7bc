using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Xml.Serialization;
using ChatLauncher.Models;

namespace ChatLauncher.Services
{
    /// <summary>
    /// 配置文件管理服务
    /// </summary>
    public class ConfigurationManager
    {
        private const string CONFIG_FILE_NAME = "configs.xml";
        private const string DEFAULT_CONFIG_FILE_NAME = "default_configs.xml";
        private readonly string _configDirectory;
        private readonly string _configFilePath;
        private readonly string _defaultConfigFilePath;

        public ConfigurationManager()
        {
            // 配置文件存储在应用程序目录下的Configs文件夹
            _configDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Configs");
            _configFilePath = Path.Combine(_configDirectory, CONFIG_FILE_NAME);
            _defaultConfigFilePath = Path.Combine(_configDirectory, DEFAULT_CONFIG_FILE_NAME);

            // 确保配置目录存在
            if (!Directory.Exists(_configDirectory))
            {
                Directory.CreateDirectory(_configDirectory);
            }

            // 初始化默认配置文件
            InitializeDefaultConfigs();
        }

        /// <summary>
        /// 初始化默认配置文件
        /// </summary>
        private void InitializeDefaultConfigs()
        {
            // 只在默认配置文件不存在时才创建
            if (!File.Exists(_defaultConfigFilePath))
            {
                var defaultConfigs = GetBuiltInDefaultConfigs();
                SaveDefaultConfigs(defaultConfigs);
            }
        }

        /// <summary>
        /// 获取内置默认配置
        /// </summary>
        private List<LaunchConfig> GetBuiltInDefaultConfigs()
        {
            return new List<LaunchConfig>
            {
                new LaunchConfig
                {
                    Name = "Discord Web",
                    Url = "https://discord.com/app",
                    Description = "启动并访问Discord Web",
                    Icon = "🎮",
                    Debug = false,
                    UseCount = 0,
                    CreatedTime = DateTime.Now,
                    IsDefault = true
                },
                new LaunchConfig
                {
                    Name = "Facebook Messenger",
                    Url = "https://www.messenger.com",
                    Description = "启动并访问Facebook Messenger",
                    Icon = "📘",
                    Debug = false,
                    UseCount = 0,
                    CreatedTime = DateTime.Now,
                    IsDefault = true
                },
                new LaunchConfig
                {
                    Name = "Telegram Web",
                    Url = "https://web.telegram.org",
                    Description = "启动并访问Telegram Web",
                    Icon = "✈️",
                    Debug = false,
                    UseCount = 0,
                    CreatedTime = DateTime.Now,
                    IsDefault = true
                },
                new LaunchConfig
                {
                    Name = "WhatsApp Web",
                    Url = "https://web.whatsapp.com",
                    Description = "启动并访问WhatsApp Web",
                    Icon = "💬",
                    Debug = false,
                    UseCount = 0,
                    CreatedTime = DateTime.Now,
                    IsDefault = true
                },
                new LaunchConfig
                {
                    Name = "代理测试 (HTTP)",
                    Url = "https://www.browserscan.net/zh",
                    Proxy = "http://127.0.0.1:8080",
                    Description = "使用HTTP代理测试",
                    Icon = "🌐",
                    Debug = true,
                    UseCount = 0,
                    CreatedTime = DateTime.Now,
                    IsDefault = true
                },
                new LaunchConfig
                {
                    Name = "代理测试 (SOCKS5)",
                    Url = "https://www.browserscan.net/zh",
                    Proxy = "socks5://127.0.0.1:1080",
                    Description = "使用SOCKS5代理测试",
                    Icon = "🔒",
                    Debug = true,
                    UseCount = 0,
                    CreatedTime = DateTime.Now,
                    IsDefault = true
                },
                new LaunchConfig
                {
                    Name = "浏览器指纹效测试",
                    Url = "https://www.browserscan.net/zh",
                    Description = "测试浏览器指纹伪代理设置",
                    Icon = "🔍",
                    Debug = true,
                    Width = 1280,
                    Height = 720,
                    UseCount = 0,
                    CreatedTime = DateTime.Now,
                    IsDefault = true
                },
                new LaunchConfig
                {
                    Name = "默认启动",
                    Description = "不带任何参数的默认启动",
                    Icon = "🚀",
                    Debug = false,
                    UseCount = 0,
                    CreatedTime = DateTime.Now,
                    IsDefault = true
                }
            };
        }

        /// <summary>
        /// 加载所有配置
        /// </summary>
        public List<LaunchConfig> LoadConfigs()
        {
            var allConfigs = new List<LaunchConfig>();

            try
            {
                // 加载默认配置
                var defaultConfigs = LoadDefaultConfigs();
                allConfigs.AddRange(defaultConfigs);

                // 加载用户自定义配置
                var userConfigs = LoadUserConfigs();
                allConfigs.AddRange(userConfigs);

                return allConfigs.OrderByDescending(c => c.IsFavorite)
                                .ThenByDescending(c => c.UseCount)
                                .ThenBy(c => c.Name)
                                .ToList();
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to load configurations: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 加载默认配置
        /// </summary>
        private List<LaunchConfig> LoadDefaultConfigs()
        {
            try
            {
                if (File.Exists(_defaultConfigFilePath))
                {
                    var serializer = new XmlSerializer(typeof(List<LaunchConfig>));
                    using (var reader = new FileStream(_defaultConfigFilePath, FileMode.Open))
                    {
                        var configs = (List<LaunchConfig>)serializer.Deserialize(reader) ?? new List<LaunchConfig>();

                        // 确保默认配置标记
                        foreach (var config in configs)
                        {
                            config.IsDefault = true;
                            // 确保空值显示为合适的默认值
                            if (string.IsNullOrEmpty(config.Url))
                                config.Url = null;
                            if (string.IsNullOrEmpty(config.Proxy))
                                config.Proxy = null;
                        }

                        return configs;
                    }
                }
                else
                {
                    return GetBuiltInDefaultConfigs();
                }
            }
            catch (Exception ex)
            {
                // 如果加载失败，返回内置默认配置
                return GetBuiltInDefaultConfigs();
            }
        }

        /// <summary>
        /// 加载用户配置
        /// </summary>
        private List<LaunchConfig> LoadUserConfigs()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    var serializer = new XmlSerializer(typeof(List<LaunchConfig>));
                    using (var reader = new FileStream(_configFilePath, FileMode.Open))
                    {
                        var configs = (List<LaunchConfig>)serializer.Deserialize(reader) ?? new List<LaunchConfig>();

                        // 确保用户配置标记
                        foreach (var config in configs)
                        {
                            config.IsDefault = false;
                        }

                        return configs;
                    }
                }
                else
                {
                    return new List<LaunchConfig>();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to load user configurations: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 保存默认配置
        /// </summary>
        private void SaveDefaultConfigs(List<LaunchConfig> configs)
        {
            try
            {
                var serializer = new XmlSerializer(typeof(List<LaunchConfig>));
                using (var writer = new FileStream(_defaultConfigFilePath, FileMode.Create))
                {
                    serializer.Serialize(writer, configs);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to save default configurations: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 保存用户配置
        /// </summary>
        public void SaveUserConfigs(List<LaunchConfig> userConfigs)
        {
            try
            {
                // 只保存用户自定义的配置
                var configsToSave = userConfigs.Where(c => !c.IsDefault).ToList();
                var serializer = new XmlSerializer(typeof(List<LaunchConfig>));
                using (var writer = new FileStream(_configFilePath, FileMode.Create))
                {
                    serializer.Serialize(writer, configsToSave);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to save user configurations: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 添加或更新配置
        /// </summary>
        public void SaveConfig(LaunchConfig config)
        {
            try
            {
                var userConfigs = LoadUserConfigs();
                
                // 查找是否已存在相同名称的配置
                var existingConfig = userConfigs.FirstOrDefault(c => c.Name == config.Name);
                if (existingConfig != null)
                {
                    // 更新现有配置
                    var index = userConfigs.IndexOf(existingConfig);
                    userConfigs[index] = config;
                }
                else
                {
                    // 添加新配置
                    config.IsDefault = false;
                    config.CreatedTime = DateTime.Now;
                    userConfigs.Add(config);
                }

                SaveUserConfigs(userConfigs);
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to save configuration: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 删除配置
        /// </summary>
        public void DeleteConfig(LaunchConfig config)
        {
            try
            {
                if (config.IsDefault)
                {
                    throw new InvalidOperationException("Cannot delete default configuration");
                }

                var userConfigs = LoadUserConfigs();
                var configToRemove = userConfigs.FirstOrDefault(c => c.Name == config.Name);
                if (configToRemove != null)
                {
                    userConfigs.Remove(configToRemove);
                    SaveUserConfigs(userConfigs);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to delete configuration: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新配置使用统计
        /// </summary>
        public void UpdateConfigUsage(LaunchConfig config)
        {
            try
            {
                config.UseCount++;
                config.LastUsedTime = DateTime.Now;

                if (!config.IsDefault)
                {
                    SaveConfig(config);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to update configuration usage: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        public string GetConfigFilePath()
        {
            return _configFilePath;
        }

        /// <summary>
        /// 获取默认配置文件路径
        /// </summary>
        public string GetDefaultConfigFilePath()
        {
            return _defaultConfigFilePath;
        }
    }
}
