@echo off
chcp 65001 > nul
echo ========================================
echo C# 7.3 Compatibility Test
echo ========================================
echo.

echo Checking for C# 8.0+ syntax that needs to be fixed...
echo.

echo 1. Checking for null-coalescing assignment (??=)...
findstr /R "??=" ChatLauncher\*.cs ChatLauncher\Models\*.cs ChatLauncher\Services\*.cs ChatLauncher\ViewModels\*.cs 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✗ Found ??= operators that need to be fixed
) else (
    echo ✓ No ??= operators found
)

echo.
echo 2. Checking for range operators ([..])...
findstr /R "\[\.\..*\]" ChatLauncher\*.cs ChatLauncher\Models\*.cs ChatLauncher\Services\*.cs <PERSON>t<PERSON>auncher\ViewModels\*.cs 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✗ Found range operators that need to be fixed
) else (
    echo ✓ No range operators found
)

echo.
echo 3. Checking for switch expressions...
findstr /R "switch.*=>" ChatLauncher\*.cs ChatLauncher\Models\*.cs ChatLauncher\Services\*.cs ChatLauncher\ViewModels\*.cs 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✗ Found switch expressions that need to be fixed
) else (
    echo ✓ No switch expressions found
)

echo.
echo 4. Checking for using declarations...
findstr /R "using.*[^;]$" ChatLauncher\*.cs ChatLauncher\Models\*.cs ChatLauncher\Services\*.cs ChatLauncher\ViewModels\*.cs 2>nul | findstr /V "using System" | findstr /V "using ChatLauncher" | findstr /V "namespace"
if %ERRORLEVEL% EQU 0 (
    echo ✗ Found potential using declarations that need to be checked
) else (
    echo ✓ No problematic using declarations found
)

echo.
echo 5. Testing compilation with C# 7.3 compatibility...
echo.

echo Cleaning previous build...
if exist "ChatLauncher\bin" rmdir /s /q "ChatLauncher\bin"
if exist "ChatLauncher\obj" rmdir /s /q "ChatLauncher\obj"

echo.
echo Compiling ChatLauncher project...
msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /p:LangVersion=7.3 /v:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ ChatLauncher compiled successfully with C# 7.3!
    
    if exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
        echo ✓ Output file created: ChatLauncher\bin\Debug\ChatLauncher.exe
        
        echo.
        echo File information:
        dir "ChatLauncher\bin\Debug\ChatLauncher.exe"
        
        echo.
        echo ========================================
        echo SUCCESS: C# 7.3 Compatibility Confirmed
        echo ========================================
        echo.
        echo The project compiles successfully with C# 7.3 syntax.
        echo You can now run: ChatLauncher\bin\Debug\ChatLauncher.exe
        echo.
        
        set /p choice=Do you want to start ChatLauncher now? (y/n): 
        if /i "%choice%"=="y" (
            echo Starting ChatLauncher...
            start ChatLauncher\bin\Debug\ChatLauncher.exe
        )
    ) else (
        echo ✗ Output file not found
    )
) else (
    echo.
    echo ✗ Compilation failed!
    echo.
    echo Common C# 7.3 compatibility issues:
    echo 1. ??= operator - use if (var == null) var = value; instead
    echo 2. Range operators [..] - use .Substring() instead
    echo 3. Switch expressions - use traditional switch statements
    echo 4. Using declarations - use traditional using statements with braces
    echo 5. Default interface methods - not supported in C# 7.3
    echo.
    echo Please check the error messages above and fix any compatibility issues.
)

echo.
echo ========================================
echo Test Complete
echo ========================================
pause
