using Chat.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Chat.Services
{
    /// <summary>
    /// 浏览器指纹服务接口
    /// </summary>
    public interface IBrowserFingerprintService
    {
        /// <summary>
        /// 获取所有指纹配置
        /// </summary>
        Task<List<BrowserFingerprint>> GetAllFingerprintsAsync();

        /// <summary>
        /// 根据ID获取指纹配置
        /// </summary>
        Task<BrowserFingerprint> GetFingerprintByIdAsync(string id);

        /// <summary>
        /// 保存指纹配置
        /// </summary>
        Task<bool> SaveFingerprintAsync(BrowserFingerprint fingerprint);

        /// <summary>
        /// 删除指纹配置
        /// </summary>
        Task<bool> DeleteFingerprintAsync(string id);

        /// <summary>
        /// 创建默认指纹配置
        /// </summary>
        BrowserFingerprint CreateDefaultFingerprint();

        /// <summary>
        /// 生成随机指纹配置
        /// </summary>
        BrowserFingerprint GenerateRandomFingerprint();

        /// <summary>
        /// 应用指纹到浏览器
        /// </summary>
        Task<bool> ApplyFingerprintToBrowserAsync(BrowserFingerprint fingerprint, CefSharp.Wpf.ChromiumWebBrowser browser);

        /// <summary>
        /// 生成指纹注入脚本
        /// </summary>
        string GenerateFingerprintScript(BrowserFingerprint fingerprint);

        /// <summary>
        /// 导入指纹配置
        /// </summary>
        Task<bool> ImportFingerprintAsync(string filePath);

        /// <summary>
        /// 导出指纹配置
        /// </summary>
        Task<bool> ExportFingerprintAsync(BrowserFingerprint fingerprint, string filePath);

        /// <summary>
        /// 验证指纹配置
        /// </summary>
        bool ValidateFingerprint(BrowserFingerprint fingerprint);
    }
}
