@echo off
chcp 65001 > nul
echo ========================================
echo Chat.exe Path Diagnosis Tool
echo ========================================
echo.

echo Current working directory: %CD%
echo.

echo Checking for Chat.exe in various locations...
echo.

echo 1. Main project Debug build:
if exist "bin\x86\Debug\Chat.exe" (
    echo ✓ Found: bin\x86\Debug\Chat.exe
    dir "bin\x86\Debug\Chat.exe"
) else (
    echo ✗ Not found: bin\x86\Debug\Chat.exe
)

echo.
echo 2. Main project Release build:
if exist "bin\x86\Release\Chat.exe" (
    echo ✓ Found: bin\x86\Release\Chat.exe
    dir "bin\x86\Release\Chat.exe"
) else (
    echo ✗ Not found: bin\x86\Release\Chat.exe
)

echo.
echo 3. ChatLauncher working directory:
echo ChatLauncher.exe location: ChatLauncher\bin\Debug\ChatLauncher.exe
if exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
    echo ✓ ChatLauncher.exe exists
) else (
    echo ✗ ChatLauncher.exe not found
)

echo.
echo 4. Relative paths from ChatLauncher location:
echo From ChatLauncher\bin\Debug\ to Chat.exe:

echo Checking: ..\..\bin\x86\Debug\Chat.exe
pushd "ChatLauncher\bin\Debug" 2>nul
if exist "..\..\bin\x86\Debug\Chat.exe" (
    echo ✓ Found via relative path: ..\..\bin\x86\Debug\Chat.exe
    dir "..\..\bin\x86\Debug\Chat.exe"
) else (
    echo ✗ Not found via relative path: ..\..\bin\x86\Debug\Chat.exe
)
popd 2>nul

echo.
echo 5. Full path resolution test:
echo Resolving paths from ChatLauncher\bin\Debug\:

pushd "ChatLauncher\bin\Debug" 2>nul
for %%p in (
    "..\bin\x86\Debug\Chat.exe"
    "..\..\bin\x86\Debug\Chat.exe"
    "..\..\..\bin\x86\Debug\Chat.exe"
) do (
    echo Testing: %%p
    if exist "%%p" (
        echo   ✓ EXISTS: %%p
        for %%f in ("%%p") do echo   Full path: %%~ff
    ) else (
        echo   ✗ NOT FOUND: %%p
    )
)
popd 2>nul

echo.
echo 6. Directory structure analysis:
echo.
echo Root directory contents:
dir /b | findstr /i "bin chat"

echo.
echo ChatLauncher directory contents:
if exist "ChatLauncher" (
    dir /b ChatLauncher | findstr /i "bin"
) else (
    echo ChatLauncher directory not found
)

echo.
echo 7. Recommended solution:
echo.
echo Based on the analysis above, the correct relative path from
echo ChatLauncher\bin\Debug\ to Chat.exe should be:
echo.
if exist "bin\x86\Debug\Chat.exe" (
    echo ✓ Use: ..\..\bin\x86\Debug\Chat.exe
    echo   This goes up 2 levels from ChatLauncher\bin\Debug\ to reach the root,
    echo   then down to bin\x86\Debug\Chat.exe
) else (
    echo ✗ Chat.exe not found in expected location
    echo   Please compile the Chat project first:
    echo   msbuild Chat.csproj /p:Configuration=Debug /p:Platform=x86
)

echo.
echo 8. Testing ChatLauncher after fix:
echo.
if exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
    echo Recompiling ChatLauncher with fixed paths...
    msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal
    
    if %ERRORLEVEL% EQU 0 (
        echo ✓ ChatLauncher recompiled successfully
        echo.
        set /p choice=Do you want to test ChatLauncher now? (y/n): 
        if /i "%choice%"=="y" (
            echo Starting ChatLauncher...
            start ChatLauncher\bin\Debug\ChatLauncher.exe
        )
    ) else (
        echo ✗ ChatLauncher compilation failed
    )
) else (
    echo ChatLauncher.exe not found, please compile it first
)

echo.
echo ========================================
echo Diagnosis Complete
echo ========================================
pause
