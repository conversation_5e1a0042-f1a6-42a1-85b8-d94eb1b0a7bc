@echo off
echo ========================================
echo 多实例支持测试工具
echo ========================================
echo.

echo 请选择测试场景：
echo 1. 启动单个实例（默认）
echo 2. 启动2个实例
echo 3. 启动3个实例
echo 4. 启动5个实例
echo 5. 启动实例并使用不同代理
echo 6. 启动实例并访问不同网站
echo 7. 查看当前运行的实例
echo 8. 退出
echo.

set /p choice=请输入选择 (1-8): 

if "%choice%"=="1" goto single_instance
if "%choice%"=="2" goto two_instances
if "%choice%"=="3" goto three_instances
if "%choice%"=="4" goto five_instances
if "%choice%"=="5" goto different_proxy
if "%choice%"=="6" goto different_sites
if "%choice%"=="7" goto list_instances
if "%choice%"=="8" goto exit

echo 无效选择，启动单个实例...
goto single_instance

:single_instance
echo.
echo 启动单个实例...
start bin\x86\Debug\Chat.exe --debug
goto end

:two_instances
echo.
echo 启动2个实例...
start bin\x86\Debug\Chat.exe --debug
timeout /t 2 /nobreak > nul
start bin\x86\Debug\Chat.exe --debug
goto end

:three_instances
echo.
echo 启动3个实例...
start bin\x86\Debug\Chat.exe --debug
timeout /t 2 /nobreak > nul
start bin\x86\Debug\Chat.exe --debug
timeout /t 2 /nobreak > nul
start bin\x86\Debug\Chat.exe --debug
goto end

:five_instances
echo.
echo 启动5个实例...
for /l %%i in (1,1,5) do (
    echo 启动实例 %%i...
    start bin\x86\Debug\Chat.exe --debug
    timeout /t 1 /nobreak > nul
)
goto end

:different_proxy
echo.
echo 启动实例并使用不同代理...
echo 实例1: 无代理
start bin\x86\Debug\Chat.exe --url https://www.browserscan.net/zh --debug

timeout /t 2 /nobreak > nul
echo 实例2: HTTP代理 127.0.0.1:8080
start bin\x86\Debug\Chat.exe --proxy http://127.0.0.1:8080 --url https://www.browserscan.net/zh --debug

timeout /t 2 /nobreak > nul
echo 实例3: SOCKS5代理 127.0.0.1:1080
start bin\x86\Debug\Chat.exe --proxy socks5://127.0.0.1:1080 --url https://www.browserscan.net/zh --debug
goto end

:different_sites
echo.
echo 启动实例并访问不同网站...
echo 实例1: WhatsApp Web
start bin\x86\Debug\Chat.exe --url https://web.whatsapp.com --debug

timeout /t 2 /nobreak > nul
echo 实例2: Telegram Web
start bin\x86\Debug\Chat.exe --url https://web.telegram.org --debug

timeout /t 2 /nobreak > nul
echo 实例3: Facebook Messenger
start bin\x86\Debug\Chat.exe --url https://www.messenger.com --debug

timeout /t 2 /nobreak > nul
echo 实例4: 浏览器指纹测试
start bin\x86\Debug\Chat.exe --url https://www.browserscan.net/zh --debug
goto end

:list_instances
echo.
echo 当前运行的Chat.exe实例：
echo ========================================
tasklist /fi "imagename eq Chat.exe" /fo table
echo.
echo 实例数据目录：
echo ========================================
if exist "%LOCALAPPDATA%\ChatApp\CefSharp" (
    dir "%LOCALAPPDATA%\ChatApp\CefSharp" /b
) else (
    echo 未找到实例数据目录
)
echo.
echo 按任意键返回主菜单...
pause > nul
goto :eof

:exit
echo 退出测试工具
goto end

:end
echo.
echo 测试启动完成！
echo.
echo 提示：
echo - 每个实例都有独立的用户数据目录
echo - 可以同时使用不同的代理设置
echo - 可以同时访问不同的网站
echo - 实例ID会显示在窗口标题中
echo.
echo 按任意键退出...
pause > nul
