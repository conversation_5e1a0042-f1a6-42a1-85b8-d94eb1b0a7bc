using CefSharp;
using CefSharp.Wpf;
using Chat.Models;
using System;
using System.IO;

namespace Chat.Services
{
    /// <summary>
    /// CefSharp代理配置服务
    /// </summary>
    public static class CefSharpProxyService
    {
        private static ProxyConfig _currentProxy;
        private static bool _isInitialized = false;

        /// <summary>
        /// 初始化CefSharp代理设置
        /// </summary>
        public static void InitializeProxy(ProxyConfig proxyConfig)
        {
            if (_isInitialized)
            {
                throw new InvalidOperationException("CefSharp代理已经初始化，无法重复设置");
            }

            _currentProxy = proxyConfig ?? new ProxyConfig();

            try
            {
                // 配置CefSharp设置
                var settings = new CefSettings();

                // 基本设置
                settings.CefCommandLineArgs.Add("--disable-web-security");
                settings.CefCommandLineArgs.Add("--disable-features=VizDisplayCompositor");
                settings.CefCommandLineArgs.Add("--disable-gpu-vsync");

                // 代理设置
                if (_currentProxy.Enabled && _currentProxy.IsValid())
                {
                    ConfigureProxySettings(settings, _currentProxy);
                    System.Diagnostics.Debug.WriteLine($"已配置代理: {_currentProxy.GetDisplayName()}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("未配置代理，使用系统默认设置");
                }

                // 设置用户数据目录
                var userDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "ChatApp", "CefSharp");
                settings.RootCachePath = userDataPath;

                // 初始化CefSharp
                if (Cef.IsInitialized != null && Cef.IsInitialized.Value)
                {
                    Cef.Initialize(settings);
                }

                _isInitialized = true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化CefSharp代理失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 配置代理设置
        /// </summary>
        private static void ConfigureProxySettings(CefSettings settings, ProxyConfig proxy)
        {
            // 设置代理服务器
            var proxyServer = $"{proxy.Host}:{proxy.Port}";
            
            switch (proxy.Type)
            {
                case ProxyType.Http:
                case ProxyType.Https:
                    settings.CefCommandLineArgs.Add("--proxy-server", $"http://{proxyServer}");
                    break;
                    
                case ProxyType.Socks5:
                    settings.CefCommandLineArgs.Add("--proxy-server", $"socks5://{proxyServer}");
                    break;
                    
                case ProxyType.Socks4:
                    settings.CefCommandLineArgs.Add("--proxy-server", $"socks4://{proxyServer}");
                    break;
            }

            // 设置代理绕过列表
            if (!string.IsNullOrEmpty(proxy.BypassList))
            {
                settings.CefCommandLineArgs.Add("--proxy-bypass-list", proxy.BypassList);
            }

            // 如果有认证信息，需要通过RequestHandler处理
            if (!string.IsNullOrEmpty(proxy.Username) && !string.IsNullOrEmpty(proxy.Password))
            {
                // 注意：CefSharp的代理认证需要通过RequestHandler的GetAuthCredentials方法处理
                System.Diagnostics.Debug.WriteLine("代理认证信息已设置，将在RequestHandler中处理");
            }
        }

        /// <summary>
        /// 创建带代理认证的RequestHandler
        /// </summary>
        public static IRequestHandler CreateProxyRequestHandler()
        {
            if (_currentProxy?.Enabled == true && 
                !string.IsNullOrEmpty(_currentProxy.Username) && 
                !string.IsNullOrEmpty(_currentProxy.Password))
            {
                return new ProxyAuthRequestHandler(_currentProxy.Username, _currentProxy.Password);
            }

            return null;
        }

        /// <summary>
        /// 获取当前代理配置
        /// </summary>
        public static ProxyConfig GetCurrentProxy()
        {
            return _currentProxy;
        }

        /// <summary>
        /// 检查代理是否已初始化
        /// </summary>
        public static bool IsInitialized => _isInitialized;

        /// <summary>
        /// 为ChromiumWebBrowser配置代理
        /// </summary>
        public static void ConfigureBrowser(ChromiumWebBrowser browser)
        {
            if (browser == null)
                return;

            try
            {
                // 设置RequestHandler以处理代理认证
                var requestHandler = CreateProxyRequestHandler();
                if (requestHandler != null)
                {
                    browser.RequestHandler = requestHandler;
                }

                // 设置其他浏览器选项
                browser.BrowserSettings = new BrowserSettings();

                System.Diagnostics.Debug.WriteLine("浏览器代理配置完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"配置浏览器代理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试代理连接
        /// </summary>
        public static bool TestProxyConnection(ProxyConfig proxy, int timeoutMs = 5000)
        {
            if (!proxy.Enabled || !proxy.IsValid())
            {
                return true; // 无代理时认为连接正常
            }

            try
            {
                // 这里可以实现代理连接测试逻辑
                // 由于CefSharp的限制，我们主要依赖浏览器的连接测试
                System.Diagnostics.Debug.WriteLine($"测试代理连接: {proxy.GetDisplayName()}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"代理连接测试失败: {ex.Message}");
                return false;
            }
        }
    }

    /// <summary>
    /// 代理认证RequestHandler
    /// </summary>
    public class ProxyAuthRequestHandler : IRequestHandler
    {
        private readonly string _username;
        private readonly string _password;

        public ProxyAuthRequestHandler(string username, string password)
        {
            _username = username;
            _password = password;
        }

        public bool GetAuthCredentials(IWebBrowser chromiumWebBrowser, IBrowser browser, string originUrl, bool isProxy, string host, int port, string realm, string scheme, IAuthCallback callback)
        {
            if (isProxy && !string.IsNullOrEmpty(_username) && !string.IsNullOrEmpty(_password))
            {
                callback.Continue(_username, _password);
                return true;
            }

            return false;
        }

        public bool OnBeforeBrowse(IWebBrowser chromiumWebBrowser, IBrowser browser, IFrame frame, IRequest request, bool userGesture, bool isRedirect)
        {
            return false;
        }

        public bool OnOpenUrlFromTab(IWebBrowser chromiumWebBrowser, IBrowser browser, IFrame frame, string targetUrl, WindowOpenDisposition targetDisposition, bool userGesture)
        {
            return false;
        }

        public bool OnCertificateError(IWebBrowser chromiumWebBrowser, IBrowser browser, CefErrorCode errorCode, string requestUrl, ISslInfo sslInfo, IRequestCallback callback)
        {
            return false;
        }

        public void OnPluginCrashed(IWebBrowser chromiumWebBrowser, IBrowser browser, string pluginPath)
        {
        }

        public CefReturnValue OnBeforeResourceLoad(IWebBrowser chromiumWebBrowser, IBrowser browser, IFrame frame, IRequest request, IRequestCallback callback)
        {
            return CefReturnValue.Continue;
        }

        public bool GetDownloadHandler(IWebBrowser chromiumWebBrowser, IBrowser browser, string mimeType, string fileName, long contentLength, string contentDisposition, out IDownloadHandler handler)
        {
            handler = null;
            return false;
        }

        public bool OnQuotaRequest(IWebBrowser chromiumWebBrowser, IBrowser browser, string originUrl, long newSize, IRequestCallback callback)
        {
            return false;
        }



        public bool OnResourceResponse(IWebBrowser chromiumWebBrowser, IBrowser browser, IFrame frame, IRequest request, IResponse response)
        {
            return false;
        }

        public IResponseFilter GetResourceResponseFilter(IWebBrowser chromiumWebBrowser, IBrowser browser, IFrame frame, IRequest request, IResponse response)
        {
            return null;
        }

        public void OnResourceLoadComplete(IWebBrowser chromiumWebBrowser, IBrowser browser, IFrame frame, IRequest request, IResponse response, UrlRequestStatus status, long receivedContentLength)
        {
        }

        public void OnProtocolExecution(IWebBrowser chromiumWebBrowser, IBrowser browser, IFrame frame, IRequest request)
        {
        }

        public void OnResourceRedirect(IWebBrowser chromiumWebBrowser, IBrowser browser, IFrame frame, IRequest request, IResponse response, ref string newUrl)
        {
        }

        public bool OnSelectClientCertificate(IWebBrowser chromiumWebBrowser, IBrowser browser, bool isProxy, string host, int port, System.Security.Cryptography.X509Certificates.X509Certificate2Collection certificates, ISelectClientCertificateCallback callback)
        {
            return false;
        }

        public void OnDocumentAvailableInMainFrame(IWebBrowser chromiumWebBrowser, IBrowser browser)
        {
        }

        public IResourceRequestHandler GetResourceRequestHandler(IWebBrowser chromiumWebBrowser, IBrowser browser, IFrame frame, IRequest request, bool isNavigation, bool isDownload, string requestInitiator, ref bool disableDefaultHandling)
        {
            return null;
        }

        public void OnRenderViewReady(IWebBrowser chromiumWebBrowser, IBrowser browser)
        {
        }

        public void OnRenderProcessTerminated(IWebBrowser chromiumWebBrowser, IBrowser browser, CefTerminationStatus status, int errorCode, string errorString)
        {
        }
    }
}
