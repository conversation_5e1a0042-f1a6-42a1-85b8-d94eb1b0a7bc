@echo off
chcp 65001 > nul
echo ========================================
echo Fixed Compilation Test
echo ========================================
echo.

echo Step 1: Clean previous build files...
if exist "ChatLauncher\bin" rmdir /s /q "ChatLauncher\bin"
if exist "ChatLauncher\obj" rmdir /s /q "ChatLauncher\obj"
echo Build folders cleaned.
echo.

echo Step 2: Verify all required files exist...
set "missing_files="

if not exist "ChatLauncher\ChatLauncher.csproj" (
    echo ✗ ChatLauncher.csproj not found
    set "missing_files=1"
) else (
    echo ✓ ChatLauncher.csproj found
)

if not exist "ChatLauncher\App.xaml" (
    echo ✗ App.xaml not found
    set "missing_files=1"
) else (
    echo ✓ App.xaml found
)

if not exist "ChatLauncher\App.xaml.cs" (
    echo ✗ App.xaml.cs not found
    set "missing_files=1"
) else (
    echo ✓ App.xaml.cs found
)

if not exist "ChatLauncher\MainWindow.xaml" (
    echo ✗ MainWindow.xaml not found
    set "missing_files=1"
) else (
    echo ✓ MainWindow.xaml found
)

if not exist "ChatLauncher\MainWindow.xaml.cs" (
    echo ✗ MainWindow.xaml.cs not found
    set "missing_files=1"
) else (
    echo ✓ MainWindow.xaml.cs found
)

if not exist "ChatLauncher\ViewModels\MainViewModel.cs" (
    echo ✗ MainViewModel.cs not found
    set "missing_files=1"
) else (
    echo ✓ MainViewModel.cs found
)

if not exist "ChatLauncher\Models\LaunchConfig.cs" (
    echo ✗ LaunchConfig.cs not found
    set "missing_files=1"
) else (
    echo ✓ LaunchConfig.cs found
)

if not exist "ChatLauncher\Services\ChatAppLauncher.cs" (
    echo ✗ ChatAppLauncher.cs not found
    set "missing_files=1"
) else (
    echo ✓ ChatAppLauncher.cs found
)

if defined missing_files (
    echo.
    echo ERROR: Some required files are missing!
    pause
    exit /b 1
)

echo.
echo All required files found!
echo.

echo Step 3: Test ChatLauncher compilation...
echo Command: msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal
echo.

msbuild ChatLauncher\ChatLauncher.csproj /p:Configuration=Debug /p:Platform=AnyCPU /v:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ ChatLauncher compiled successfully!
    
    if exist "ChatLauncher\bin\Debug\ChatLauncher.exe" (
        echo ✓ ChatLauncher.exe created successfully
        
        echo.
        echo File information:
        dir "ChatLauncher\bin\Debug\ChatLauncher.exe"
        
        echo.
        echo Dependencies check:
        if exist "ChatLauncher\bin\Debug\ChatLauncher.exe.config" (
            echo ✓ App.config found
        ) else (
            echo ⚠ App.config not found (may be optional)
        )
        
        echo.
        echo ========================================
        echo SUCCESS: Compilation Fixed!
        echo ========================================
        echo.
        echo The NullReferenceException has been fixed.
        echo ChatLauncher compiled successfully.
        echo.
        echo You can now run: ChatLauncher\bin\Debug\ChatLauncher.exe
        echo.
        
        set /p choice=Do you want to start ChatLauncher now? (y/n): 
        if /i "%choice%"=="y" (
            echo.
            echo Starting ChatLauncher...
            echo If you encounter any issues, check the console output.
            echo.
            start ChatLauncher\bin\Debug\ChatLauncher.exe
            echo.
            echo ChatLauncher started! Check if the window appears.
        )
    ) else (
        echo ✗ ChatLauncher.exe not found in output directory
        echo This might indicate a compilation issue.
    )
) else (
    echo.
    echo ✗ ChatLauncher compilation failed!
    echo.
    echo Common issues and solutions:
    echo 1. NullReferenceException - Fixed in App.xaml.cs
    echo 2. Missing references - Check project dependencies
    echo 3. XAML errors - Check MainWindow.xaml syntax
    echo 4. C# syntax errors - Check for C# 7.3 compatibility
    echo.
    echo Please check the error messages above for specific issues.
)

echo.
echo ========================================
echo Test Complete
echo ========================================
pause
