<Window x:Class="Chat.TestChatExtraction"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:wpf="clr-namespace:CefSharp.Wpf;assembly=CefSharp.Wpf"
        Title="测试聊天数据提取" Height="600" Width="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="200"/>
        </Grid.RowDefinitions>
        
        <!-- 控制栏 -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="10">
            <StackPanel Orientation="Horizontal">
                <ComboBox x:Name="PlatformComboBox" Width="150" Margin="0,0,10,0">
                    <ComboBoxItem Content="WhatsApp Web" Tag="https://web.whatsapp.com/"/>
                    <ComboBoxItem Content="Telegram Web" Tag="https://web.telegram.org/"/>
                    <ComboBoxItem Content="Discord" Tag="https://discord.com/app"/>
                </ComboBox>
                <Button x:Name="LoadButton" Content="加载平台" Margin="0,0,10,0" Click="LoadButton_Click"/>
                <Button x:Name="StartExtractionButton" Content="开始提取" Margin="0,0,10,0" Click="StartExtractionButton_Click"/>
                <Button x:Name="StopExtractionButton" Content="停止提取" Margin="0,0,10,0" Click="StopExtractionButton_Click"/>
                <TextBlock x:Name="StatusText" Text="就绪" Foreground="White" VerticalAlignment="Center" Margin="20,0,0,0"/>
            </StackPanel>
        </Border>
        
        <!-- 浏览器 -->
        <wpf:ChromiumWebBrowser x:Name="Browser" Grid.Row="1"/>
        
        <!-- 提取的消息 -->
        <Border Grid.Row="2" Background="#34495E">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="200"/>
                </Grid.ColumnDefinitions>
                
                <ScrollViewer Grid.Column="0" VerticalScrollBarVisibility="Auto">
                    <ListBox x:Name="ExtractedMessagesList" Background="Transparent" BorderThickness="0" Margin="10">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Background="#3498DB" CornerRadius="5" Padding="8" Margin="0,2">
                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="{Binding Sender}" FontWeight="Bold" 
                                                      Foreground="White" Margin="0,0,10,0"/>
                                            <TextBlock Text="{Binding Timestamp, StringFormat=HH:mm:ss}" 
                                                      FontSize="10" Foreground="#BDC3C7"/>
                                        </StackPanel>
                                        <TextBlock Text="{Binding Content}" Foreground="White" 
                                                  TextWrapping="Wrap" Margin="0,3,0,0"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </ScrollViewer>
                
                <Border Grid.Column="1" Background="#2C3E50" Padding="10">
                    <StackPanel>
                        <TextBlock Text="统计信息" Foreground="White" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                        <TextBlock x:Name="MessageCountText" Text="消息数: 0" Foreground="White" Margin="0,0,0,5"/>
                        <TextBlock x:Name="LastUpdateText" Text="最后更新: --" Foreground="White" FontSize="10"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
    </Grid>
</Window>
