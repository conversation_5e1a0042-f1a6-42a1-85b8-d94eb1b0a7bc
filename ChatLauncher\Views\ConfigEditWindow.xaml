<hc:Window x:Class="ChatLauncher.Views.ConfigEditWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:hc="https://handyorg.github.io/handycontrol"
        Title="配置编辑" Height="650" Width="500"
        MinHeight="600" MinWidth="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
            <Border Background="{DynamicResource PrimaryBrush}"
                    CornerRadius="20"
                    Width="40" Height="40"
                    Margin="0,0,15,0">
                <TextBlock x:Name="TitleIcon"
                         Text="⚙️"
                         FontSize="20"
                         Foreground="White"
                         HorizontalAlignment="Center"
                         VerticalAlignment="Center"/>
            </Border>
            <StackPanel VerticalAlignment="Center">
                <TextBlock x:Name="WindowTitle"
                         Text="编辑配置"
                         FontSize="18"
                         FontWeight="Bold"
                         Foreground="{DynamicResource PrimaryTextBrush}"/>
                <TextBlock Text="配置聊天应用启动参数"
                         FontSize="12"
                         Foreground="{DynamicResource SecondaryTextBrush}"/>
            </StackPanel>
        </StackPanel>

        <!-- 配置表单 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 基本信息 -->
                <GroupBox Header="基本信息" Margin="0,0,0,15">
                    <Grid Margin="15">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0"
                                 Text="名称："
                                 VerticalAlignment="Center"
                                 Margin="0,0,0,10"/>
                        <TextBox x:Name="NameTextBox"
                               Grid.Row="0" Grid.Column="1"
                               Margin="0,0,0,10"
                               hc:InfoElement.Placeholder="输入配置名称"/>

                        <TextBlock Grid.Row="1" Grid.Column="0"
                                 Text="描述："
                                 VerticalAlignment="Top"
                                 Margin="0,5,0,10"/>
                        <TextBox x:Name="DescriptionTextBox"
                               Grid.Row="1" Grid.Column="1"
                               Height="60"
                               TextWrapping="Wrap"
                               AcceptsReturn="True"
                               VerticalScrollBarVisibility="Auto"
                               Margin="0,0,0,10"
                               hc:InfoElement.Placeholder="输入配置描述"/>

                        <TextBlock Grid.Row="2" Grid.Column="0"
                                 Text="图标："
                                 VerticalAlignment="Center"
                                 Margin="0,0,0,10"/>
                        <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                            <TextBox x:Name="IconTextBox"
                                   Width="60"
                                   MaxLength="2"
                                   FontSize="16"
                                   TextAlignment="Center"
                                   Margin="0,0,10,0"
                                   hc:InfoElement.Placeholder="🚀"/>
                            <ComboBox x:Name="IconComboBox"
                                    Width="200"
                                    SelectionChanged="IconComboBox_SelectionChanged">
                                <ComboBoxItem Content="🚀 默认启动"/>
                                <ComboBoxItem Content="💬 WhatsApp"/>
                                <ComboBoxItem Content="✈️ Telegram"/>
                                <ComboBoxItem Content="📘 Facebook"/>
                                <ComboBoxItem Content="🎮 Discord"/>
                                <ComboBoxItem Content="🌐 HTTP代理"/>
                                <ComboBoxItem Content="🔒 SOCKS5"/>
                                <ComboBoxItem Content="🔍 指纹测试"/>
                                <ComboBoxItem Content="⭐ 收藏"/>
                                <ComboBoxItem Content="🔧 自定义"/>
                            </ComboBox>
                        </StackPanel>

                        <CheckBox x:Name="IsFavoriteCheckBox"
                                Grid.Row="3" Grid.Column="1"
                                Content="设为收藏配置"
                                Margin="0,5,0,0"/>
                    </Grid>
                </GroupBox>

                <!-- 启动参数 -->
                <GroupBox Header="启动参数" Margin="0,0,0,15">
                    <Grid Margin="15">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0"
                                 Text="URL："
                                 VerticalAlignment="Center"
                                 Margin="0,0,0,10"/>
                        <TextBox x:Name="UrlTextBox"
                               Grid.Row="0" Grid.Column="1"
                               Margin="0,0,0,10"
                               hc:InfoElement.Placeholder="https://example.com"/>

                        <TextBlock Grid.Row="1" Grid.Column="0"
                                 Text="代理："
                                 VerticalAlignment="Center"
                                 Margin="0,0,0,10"/>
                        <TextBox x:Name="ProxyTextBox"
                               Grid.Row="1" Grid.Column="1"
                               Margin="0,0,0,10"
                               hc:InfoElement.Placeholder="http://proxy:port 或 socks5://proxy:port"/>

                        <TextBlock Grid.Row="2" Grid.Column="0"
                                 Text="窗口大小："
                                 VerticalAlignment="Center"
                                 Margin="0,0,0,10"/>
                        <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                            <TextBox x:Name="WidthTextBox"
                                   Width="80"
                                   hc:InfoElement.Placeholder="宽度"/>
                            <TextBlock Text=" × " VerticalAlignment="Center" Margin="5,0"/>
                            <TextBox x:Name="HeightTextBox"
                                   Width="80"
                                   hc:InfoElement.Placeholder="高度"/>
                            <CheckBox x:Name="FullscreenCheckBox"
                                    Content="全屏"
                                    Margin="15,0,0,0"
                                    VerticalAlignment="Center"/>
                        </StackPanel>

                        <TextBlock Grid.Row="3" Grid.Column="0"
                                 Text="额外参数："
                                 VerticalAlignment="Top"
                                 Margin="0,5,0,0"/>
                        <TextBox x:Name="ExtraArgsTextBox"
                               Grid.Row="3" Grid.Column="1"
                               Height="60"
                               TextWrapping="Wrap"
                               AcceptsReturn="True"
                               VerticalScrollBarVisibility="Auto"
                               hc:InfoElement.Placeholder="其他命令行参数"/>
                    </Grid>
                </GroupBox>

                <!-- 高级选项 -->
                <GroupBox Header="高级选项" Margin="0,0,0,15">
                    <StackPanel Margin="15">
                        <CheckBox x:Name="DebugCheckBox"
                                Content="启用调试模式"
                                Margin="0,0,0,10"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <TextBlock Text="用户数据目录："
                                     VerticalAlignment="Center"
                                     Width="100"/>
                            <TextBox x:Name="UserDataDirTextBox"
                                   Width="250"
                                   hc:InfoElement.Placeholder="留空使用默认"/>
                            <Button Content="浏览"
                                  Width="60"
                                  Margin="10,0,0,0"
                                  Click="BrowseUserDataDir_Click"/>
                        </StackPanel>

                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="缓存目录："
                                     VerticalAlignment="Center"
                                     Width="100"/>
                            <TextBox x:Name="CacheDirTextBox"
                                   Width="250"
                                   hc:InfoElement.Placeholder="留空使用默认"/>
                            <Button Content="浏览"
                                  Width="60"
                                  Margin="10,0,0,0"
                                  Click="BrowseCacheDir_Click"/>
                        </StackPanel>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" 
                  Orientation="Horizontal" 
                  HorizontalAlignment="Right" 
                  Margin="0,20,0,0">
            <Button x:Name="TestButton"
                  Content="🧪 测试配置"
                  Style="{StaticResource LauncherButtonStyle}"
                  MinWidth="100"
                  Margin="0,0,10,0"
                  Click="TestButton_Click"/>
            <Button x:Name="SaveButton"
                  Content="💾 保存"
                  Style="{StaticResource LauncherButtonStyle}"
                  MinWidth="80"
                  Margin="0,0,10,0"
                  Click="SaveButton_Click"/>
            <Button x:Name="CancelButton"
                  Content="❌ 取消"
                  Style="{StaticResource LauncherButtonStyle}"
                  MinWidth="80"
                  Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</hc:Window>
